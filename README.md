# Smart Car Damage Detection and Cost Estimation System

## Project Overview
An AI-powered system for automatic car damage detection and repair cost estimation tailored for the Tunisian insurance market.

## Features
- **Damage Detection**: Automatically identify and classify car damage from photos
- **Severity Assessment**: Determine damage severity (minor, moderate, severe)
- **Cost Estimation**: Predict repair costs based on Tunisian market prices
- **Insurance Integration**: Generate detailed reports for insurance claims
- **Multi-language Support**: Arabic, French, and English interfaces

## Technology Stack
- **Backend**: FastAPI (Python)
- **Frontend**: React with TypeScript
- **AI/ML**: PyTorch, YOLO, OpenCV
- **Database**: PostgreSQL
- **Deployment**: Docker, Docker Compose

## Project Structure
```
car-damage-detection/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── models/         # AI models and database models
│   │   ├── api/            # API endpoints
│   │   ├── core/           # Core functionality
│   │   └── services/       # Business logic
│   ├── data/               # Training data and datasets
│   └── requirements.txt
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   └── services/       # API services
│   └── package.json
├── models/                 # Trained AI models
├── data/                   # Datasets and training data
├── docker-compose.yml      # Docker configuration
└── README.md
```

## Getting Started

### Prerequisites
- Python 3.9+
- Node.js 16+
- Docker and Docker Compose
- CUDA-compatible GPU (recommended)

### Installation
1. Clone the repository
2. Set up backend environment
3. Set up frontend environment
4. Initialize database
5. Download pre-trained models

### Usage
1. Start the application with Docker Compose
2. Upload car damage photos
3. View damage analysis and cost estimates
4. Generate insurance reports

## Damage Types Supported
- Scratches (surface, deep)
- Dents (minor, major)
- Broken parts (bumper, headlight, mirror, etc.)
- Paint damage
- Glass damage
- Structural damage

## Cost Estimation Features
- Parts pricing based on Tunisian market
- Labor cost calculations
- Regional price variations
- Insurance deductible handling
- Repair vs replacement recommendations

## Development Roadmap
- [x] Project setup and architecture
- [ ] Core damage detection model
- [ ] Cost estimation engine
- [ ] Web application interface
- [ ] Database integration
- [ ] Testing and optimization
- [ ] Deployment configuration

## Contributing
Please read our contributing guidelines before submitting pull requests.

## License
This project is licensed under the MIT License.
