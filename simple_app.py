#!/usr/bin/env python3
"""
Simple Car Damage Detection Demo App
A simplified version that can run without Docker
"""
from fastapi import FastAPI, UploadFile, File, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
import os
from pathlib import Path
import uuid
import time
import json
from typing import List, Dict, Any

# Create FastAPI app
app = FastAPI(
    title="Car Damage Detection Demo",
    description="AI-powered car damage detection for Tunisian insurance claims",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories
Path("uploads").mkdir(exist_ok=True)
Path("static").mkdir(exist_ok=True)

# Mock damage detection function
def mock_damage_detection(image_path: str) -> Dict[str, Any]:
    """Mock damage detection that returns sample results"""
    time.sleep(2)  # Simulate processing time
    
    # Mock detected damages
    detected_damages = [
        {
            "damage_type": "scratch_surface",
            "severity": "moderate",
            "confidence": 0.85,
            "bounding_box": [100, 150, 300, 250],
            "affected_area": 5.2,
            "description": "Surface scratch detected on front bumper"
        },
        {
            "damage_type": "dent_minor",
            "severity": "minor",
            "confidence": 0.72,
            "bounding_box": [400, 200, 500, 280],
            "affected_area": 2.1,
            "description": "Minor dent on passenger door"
        }
    ]
    
    # Mock cost estimation
    parts_cost = 320.50
    labor_cost = 150.00
    paint_cost = 80.00
    tax_amount = (parts_cost + labor_cost + paint_cost) * 0.19
    total_cost = parts_cost + labor_cost + paint_cost + tax_amount
    
    return {
        "analysis_id": int(time.time()),
        "detected_damages": detected_damages,
        "overall_severity": "moderate",
        "total_estimated_cost": round(total_cost, 2),
        "confidence_score": 0.785,
        "processing_time": 2.1,
        "cost_breakdown": {
            "parts_cost": parts_cost,
            "labor_cost": labor_cost,
            "paint_cost": paint_cost,
            "tax_amount": round(tax_amount, 2),
            "total_cost": round(total_cost, 2),
            "currency": "TND"
        }
    }

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the demo homepage"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Car Damage Detection Demo</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; color: #1976d2; margin-bottom: 30px; }
            .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
            .upload-area:hover { border-color: #1976d2; background-color: #f5f5f5; }
            .button { background-color: #1976d2; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
            .button:hover { background-color: #1565c0; }
            .result { margin-top: 20px; padding: 20px; background-color: #f5f5f5; border-radius: 4px; }
            .damage-item { margin: 10px 0; padding: 10px; background-color: white; border-radius: 4px; }
            .loading { display: none; text-align: center; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🚗 Car Damage Detection Demo</h1>
            <p>AI-powered damage assessment for Tunisian insurance claims</p>
        </div>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <p>📸 Click here to upload a car damage photo</p>
            <p style="font-size: 14px; color: #666;">Supported formats: JPEG, PNG (max 10MB)</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;" onchange="uploadFile()">
        </div>
        
        <div class="loading" id="loading">
            <p>🔄 Analyzing damage... Please wait...</p>
        </div>
        
        <div id="results"></div>
        
        <script>
            async function uploadFile() {
                const fileInput = document.getElementById('fileInput');
                const file = fileInput.files[0];
                
                if (!file) return;
                
                document.getElementById('loading').style.display = 'block';
                document.getElementById('results').innerHTML = '';
                
                const formData = new FormData();
                formData.append('image', file);
                
                try {
                    const response = await fetch('/api/analyze-damage', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    displayResults(result);
                } catch (error) {
                    document.getElementById('results').innerHTML = '<div class="result"><h3>❌ Error</h3><p>Failed to analyze image. Please try again.</p></div>';
                } finally {
                    document.getElementById('loading').style.display = 'none';
                }
            }
            
            function displayResults(data) {
                const resultsDiv = document.getElementById('results');
                
                let damagesHtml = '';
                data.detected_damages.forEach(damage => {
                    damagesHtml += `
                        <div class="damage-item">
                            <strong>${damage.damage_type.replace('_', ' ').toUpperCase()}</strong> 
                            (${damage.severity}) - Confidence: ${(damage.confidence * 100).toFixed(1)}%
                            <br><small>${damage.description}</small>
                        </div>
                    `;
                });
                
                resultsDiv.innerHTML = `
                    <div class="result">
                        <h3>✅ Analysis Complete</h3>
                        <p><strong>Overall Severity:</strong> ${data.overall_severity.toUpperCase()}</p>
                        <p><strong>Total Estimated Cost:</strong> ${data.total_estimated_cost} TND</p>
                        <p><strong>Processing Time:</strong> ${data.processing_time}s</p>
                        
                        <h4>Detected Damages:</h4>
                        ${damagesHtml}
                        
                        <h4>Cost Breakdown:</h4>
                        <div class="damage-item">
                            Parts: ${data.cost_breakdown.parts_cost} TND<br>
                            Labor: ${data.cost_breakdown.labor_cost} TND<br>
                            Paint: ${data.cost_breakdown.paint_cost} TND<br>
                            Tax (19%): ${data.cost_breakdown.tax_amount} TND<br>
                            <strong>Total: ${data.cost_breakdown.total_cost} TND</strong>
                        </div>
                    </div>
                `;
            }
        </script>
    </body>
    </html>
    """
    return html_content

@app.post("/api/analyze-damage")
async def analyze_damage(image: UploadFile = File(...)):
    """Analyze car damage from uploaded image"""
    try:
        # Validate file
        if not image.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Check file size (10MB limit)
        if image.size and image.size > 10 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="File too large")
        
        # Save uploaded file
        file_extension = Path(image.filename).suffix.lower()
        if file_extension not in ['.jpg', '.jpeg', '.png', '.bmp']:
            raise HTTPException(status_code=400, detail="File type not allowed")
        
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = Path("uploads") / unique_filename
        
        with open(file_path, "wb") as buffer:
            content = await image.read()
            buffer.write(content)
        
        # Perform mock damage detection
        result = mock_damage_detection(str(file_path))
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing damage: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "Car Damage Detection Demo is running"}

@app.get("/api/docs")
async def api_docs():
    """API documentation redirect"""
    return {"message": "API documentation available at /docs"}

if __name__ == "__main__":
    print("🚗 Starting Car Damage Detection Demo...")
    print("📍 Open your browser and go to: http://localhost:8000")
    print("📚 API documentation available at: http://localhost:8000/docs")
    print("❤️  Made for Tunisian Insurance Claims")
    
    uvicorn.run(
        "simple_app:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
