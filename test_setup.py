#!/usr/bin/env python3
"""
Test script to verify the car damage detection system setup
"""
import sys
import os
import requests
import time
from pathlib import Path

def test_backend_health():
    """Test if backend is running and healthy"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend health check passed")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend not accessible: {e}")
        return False

def test_frontend():
    """Test if frontend is accessible"""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend is accessible")
            return True
        else:
            print(f"❌ Frontend not accessible: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Frontend not accessible: {e}")
        return False

def test_api_endpoints():
    """Test key API endpoints"""
    base_url = "http://localhost:8000/api/v1"
    
    # Test market prices endpoint
    try:
        response = requests.get(f"{base_url}/market-prices", timeout=5)
        if response.status_code == 200:
            print("✅ Market prices endpoint working")
        else:
            print(f"❌ Market prices endpoint failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Market prices endpoint error: {e}")
    
    # Test reports endpoint
    try:
        response = requests.get(f"{base_url}/reports", timeout=5)
        if response.status_code == 200:
            print("✅ Reports endpoint working")
        else:
            print(f"❌ Reports endpoint failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Reports endpoint error: {e}")

def test_file_structure():
    """Test if all required files and directories exist"""
    required_paths = [
        "backend/app/main.py",
        "backend/requirements.txt",
        "frontend/package.json",
        "frontend/src/App.tsx",
        "docker-compose.yml",
        "data/init.sql"
    ]
    
    all_exist = True
    for path in required_paths:
        if Path(path).exists():
            print(f"✅ {path} exists")
        else:
            print(f"❌ {path} missing")
            all_exist = False
    
    return all_exist

def test_dependencies():
    """Test if required Python packages are available"""
    required_packages = [
        "fastapi",
        "uvicorn", 
        "sqlalchemy",
        "torch",
        "ultralytics",
        "opencv-python",
        "pillow"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package} is available")
        except ImportError:
            print(f"❌ {package} is missing")
            missing_packages.append(package)
    
    return len(missing_packages) == 0

def main():
    """Run all tests"""
    print("🚗 Car Damage Detection System - Setup Test")
    print("=" * 50)
    
    # Test file structure
    print("\n📁 Testing file structure...")
    files_ok = test_file_structure()
    
    # Test Python dependencies (only if we're in the backend environment)
    if os.path.exists("backend/requirements.txt"):
        print("\n📦 Testing Python dependencies...")
        deps_ok = test_dependencies()
    else:
        deps_ok = True
        print("\n📦 Skipping dependency test (not in backend environment)")
    
    # Test services (only if they're running)
    print("\n🔧 Testing services...")
    backend_ok = test_backend_health()
    
    if backend_ok:
        test_api_endpoints()
    
    frontend_ok = test_frontend()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"File structure: {'✅ PASS' if files_ok else '❌ FAIL'}")
    print(f"Dependencies: {'✅ PASS' if deps_ok else '❌ FAIL'}")
    print(f"Backend: {'✅ PASS' if backend_ok else '❌ FAIL'}")
    print(f"Frontend: {'✅ PASS' if frontend_ok else '❌ FAIL'}")
    
    if all([files_ok, deps_ok, backend_ok, frontend_ok]):
        print("\n🎉 All tests passed! System is ready.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
