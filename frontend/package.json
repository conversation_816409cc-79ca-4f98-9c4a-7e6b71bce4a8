{"name": "car-damage-detection-frontend", "version": "1.0.0", "description": "Frontend for Car Damage Detection System", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^27.5.2", "@types/node": "^16.18.11", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.6.1", "react-scripts": "5.0.1", "typescript": "^4.9.4", "web-vitals": "^2.1.4", "axios": "^1.2.2", "react-dropzone": "^14.2.3", "react-query": "^3.39.3", "@mui/material": "^5.11.2", "@mui/icons-material": "^5.11.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "recharts": "^2.4.3", "react-image-crop": "^10.0.9", "react-toastify": "^9.1.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}