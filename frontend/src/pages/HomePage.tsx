import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  CircularProgress,
} from '@mui/material';
import {
  CloudUpload,
  Assessment,
  Receipt,
  Speed,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import ImageUpload from '../components/ImageUpload';
import VehicleInfoForm from '../components/VehicleInfoForm';
import { analyzeDamage } from '../services/api';

interface VehicleInfo {
  make: string;
  model: string;
  year: number;
}

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [vehicleInfo, setVehicleInfo] = useState<VehicleInfo>({
    make: '',
    model: '',
    year: new Date().getFullYear(),
  });
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
  };

  const handleVehicleInfoChange = (info: VehicleInfo) => {
    setVehicleInfo(info);
  };

  const handleAnalyze = async () => {
    if (!selectedFile) {
      toast.error('Please select an image first');
      return;
    }

    setIsAnalyzing(true);
    try {
      const result = await analyzeDamage(selectedFile, vehicleInfo);
      toast.success('Analysis completed successfully!');
      navigate(`/analysis/${result.analysis_id}`);
    } catch (error) {
      console.error('Analysis failed:', error);
      toast.error('Analysis failed. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const features = [
    {
      icon: <CloudUpload sx={{ fontSize: 40 }} />,
      title: 'Upload Image',
      description: 'Simply upload a photo of your damaged vehicle',
    },
    {
      icon: <Assessment sx={{ fontSize: 40 }} />,
      title: 'AI Analysis',
      description: 'Our AI detects and classifies damage automatically',
    },
    {
      icon: <Receipt sx={{ fontSize: 40 }} />,
      title: 'Cost Estimation',
      description: 'Get accurate repair cost estimates in Tunisian Dinars',
    },
    {
      icon: <Speed sx={{ fontSize: 40 }} />,
      title: 'Instant Results',
      description: 'Receive detailed reports within seconds',
    },
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Hero Section */}
      <Box textAlign="center" mb={6}>
        <Typography variant="h2" component="h1" gutterBottom fontWeight="bold">
          Smart Car Damage Detection
        </Typography>
        <Typography variant="h5" color="text.secondary" mb={4}>
          AI-powered damage assessment for Tunisian insurance claims
        </Typography>
        <Typography variant="body1" color="text.secondary" maxWidth="600px" mx="auto">
          Upload a photo of your damaged vehicle and get instant AI-powered damage analysis
          with accurate cost estimates based on Tunisian market prices.
        </Typography>
      </Box>

      {/* Features Grid */}
      <Grid container spacing={3} mb={6}>
        {features.map((feature, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card sx={{ height: '100%', textAlign: 'center', p: 2 }}>
              <CardContent>
                <Box color="primary.main" mb={2}>
                  {feature.icon}
                </Box>
                <Typography variant="h6" gutterBottom>
                  {feature.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {feature.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Main Analysis Section */}
      <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
        <Typography variant="h4" gutterBottom textAlign="center">
          Analyze Your Vehicle Damage
        </Typography>
        
        <Grid container spacing={4}>
          {/* Image Upload */}
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              1. Upload Damage Photo
            </Typography>
            <ImageUpload onFileSelect={handleFileSelect} />
          </Grid>

          {/* Vehicle Information */}
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              2. Vehicle Information (Optional)
            </Typography>
            <VehicleInfoForm
              vehicleInfo={vehicleInfo}
              onChange={handleVehicleInfoChange}
            />
          </Grid>
        </Grid>

        {/* Analyze Button */}
        <Box textAlign="center" mt={4}>
          <Button
            variant="contained"
            size="large"
            onClick={handleAnalyze}
            disabled={!selectedFile || isAnalyzing}
            startIcon={isAnalyzing ? <CircularProgress size={20} /> : <Assessment />}
            sx={{ px: 4, py: 1.5 }}
          >
            {isAnalyzing ? 'Analyzing...' : 'Analyze Damage'}
          </Button>
        </Box>
      </Paper>

      {/* Info Section */}
      <Paper elevation={1} sx={{ p: 3, bgcolor: 'primary.main', color: 'white' }}>
        <Typography variant="h5" gutterBottom>
          Why Choose Our AI System?
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Typography variant="h6" gutterBottom>
              🎯 Accurate Detection
            </Typography>
            <Typography variant="body2">
              Advanced computer vision algorithms trained specifically for automotive damage
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="h6" gutterBottom>
              💰 Tunisian Market Prices
            </Typography>
            <Typography variant="body2">
              Cost estimates based on real Tunisian automotive parts and labor rates
            </Typography>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="h6" gutterBottom>
              📋 Insurance Ready
            </Typography>
            <Typography variant="body2">
              Generate professional reports ready for insurance claim submission
            </Typography>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default HomePage;
