import React from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from 'react-query';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  Button,
  Divider,
} from '@mui/material';
import {
  Assessment,
  Receipt,
  Download,
  Share,
} from '@mui/icons-material';

import { getAnalysis, getCostBreakdown } from '../services/api';

const AnalysisPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const analysisId = parseInt(id || '0');

  const {
    data: analysis,
    isLoading: analysisLoading,
    error: analysisError,
  } = useQuery(['analysis', analysisId], () => getAnalysis(analysisId));

  const {
    data: costBreakdown,
    isLoading: costLoading,
    error: costError,
  } = useQuery(['costBreakdown', analysisId], () => getCostBreakdown(analysisId));

  if (analysisLoading || costLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading analysis results...
        </Typography>
      </Container>
    );
  }

  if (analysisError || costError) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          Failed to load analysis results. Please try again.
        </Alert>
      </Container>
    );
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'minor':
        return 'success';
      case 'moderate':
        return 'warning';
      case 'severe':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(2)} TND`;
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Damage Analysis Results
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Analysis ID: {analysis?.analysis_id} • {new Date(analysis?.created_at || '').toLocaleDateString()}
          </Typography>
        </Box>
        <Box display="flex" gap={2}>
          <Button variant="outlined" startIcon={<Share />}>
            Share
          </Button>
          <Button variant="contained" startIcon={<Download />}>
            Download Report
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Summary Cards */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Assessment color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Overall Assessment</Typography>
              </Box>
              <Chip
                label={analysis?.overall_severity?.toUpperCase()}
                color={getSeverityColor(analysis?.overall_severity || '')}
                sx={{ mb: 2 }}
              />
              <Typography variant="body2" color="text.secondary">
                Confidence: {((analysis?.confidence_score || 0) * 100).toFixed(1)}%
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Receipt color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Cost</Typography>
              </Box>
              <Typography variant="h4" color="primary" gutterBottom>
                {formatCurrency(costBreakdown?.total_cost || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Including 19% VAT
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Damages Detected
              </Typography>
              <Typography variant="h4" color="primary" gutterBottom>
                {analysis?.detected_damages?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Processing time: {(analysis?.processing_time || 0).toFixed(2)}s
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* Detected Damages */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Detected Damages
            </Typography>
            {analysis?.detected_damages?.map((damage, index) => (
              <Box key={index} sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                  <Typography variant="subtitle1" fontWeight="bold">
                    {damage.damage_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Typography>
                  <Chip
                    label={damage.severity}
                    color={getSeverityColor(damage.severity)}
                    size="small"
                  />
                </Box>
                <Typography variant="body2" color="text.secondary" mb={1}>
                  {damage.description}
                </Typography>
                <Box display="flex" gap={2}>
                  <Typography variant="caption">
                    Confidence: {(damage.confidence * 100).toFixed(1)}%
                  </Typography>
                  <Typography variant="caption">
                    Affected Area: {damage.affected_area.toFixed(1)}%
                  </Typography>
                </Box>
              </Box>
            ))}
          </Paper>
        </Grid>

        {/* Cost Breakdown */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Cost Breakdown
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Parts</Typography>
                <Typography variant="body2">
                  {formatCurrency(costBreakdown?.parts_cost || 0)}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Labor</Typography>
                <Typography variant="body2">
                  {formatCurrency(costBreakdown?.labor_cost || 0)}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Paint</Typography>
                <Typography variant="body2">
                  {formatCurrency(costBreakdown?.paint_cost || 0)}
                </Typography>
              </Box>
              <Divider sx={{ my: 1 }} />
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Subtotal</Typography>
                <Typography variant="body2">
                  {formatCurrency(
                    (costBreakdown?.parts_cost || 0) +
                    (costBreakdown?.labor_cost || 0) +
                    (costBreakdown?.paint_cost || 0)
                  )}
                </Typography>
              </Box>
              <Box display="flex" justifyContent="space-between" mb={1}>
                <Typography variant="body2">Tax (19%)</Typography>
                <Typography variant="body2">
                  {formatCurrency(costBreakdown?.tax_amount || 0)}
                </Typography>
              </Box>
              <Divider sx={{ my: 1 }} />
              <Box display="flex" justifyContent="space-between">
                <Typography variant="subtitle1" fontWeight="bold">
                  Total
                </Typography>
                <Typography variant="subtitle1" fontWeight="bold" color="primary">
                  {formatCurrency(costBreakdown?.total_cost || 0)}
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Detailed Cost Items */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Detailed Cost Items
            </Typography>
            {costBreakdown?.cost_details?.map((item, index) => (
              <Box key={index} sx={{ mb: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Typography variant="subtitle2" fontWeight="bold">
                      {item.item_name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {item.description}
                    </Typography>
                  </Box>
                  <Box textAlign="right">
                    <Typography variant="subtitle2">
                      {formatCurrency(item.total_cost)}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {item.quantity} × {formatCurrency(item.unit_cost)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            ))}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default AnalysisPage;
