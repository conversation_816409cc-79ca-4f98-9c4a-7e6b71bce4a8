import React from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip,
  Divider,
} from '@mui/material';
import {
  Psychology,
  Speed,
  Security,
  Language,
  TrendingUp,
  Support,
} from '@mui/icons-material';

const AboutPage: React.FC = () => {
  const features = [
    {
      icon: <Psychology />,
      title: 'Advanced AI Technology',
      description: 'State-of-the-art computer vision and deep learning algorithms for accurate damage detection.',
    },
    {
      icon: <Speed />,
      title: 'Instant Results',
      description: 'Get comprehensive damage analysis and cost estimates within seconds.',
    },
    {
      icon: <Security />,
      title: 'Secure & Private',
      description: 'Your data is encrypted and processed securely with full privacy protection.',
    },
    {
      icon: <Language />,
      title: 'Multi-language Support',
      description: 'Available in Arabic, French, and English to serve all Tunisian users.',
    },
    {
      icon: <TrendingUp />,
      title: 'Market-based Pricing',
      description: 'Cost estimates based on real Tunisian automotive market prices.',
    },
    {
      icon: <Support />,
      title: '24/7 Support',
      description: 'Round-the-clock customer support for all your questions and needs.',
    },
  ];

  const stats = [
    { label: 'Accuracy Rate', value: '95%' },
    { label: 'Processing Time', value: '<3s' },
    { label: 'Supported Languages', value: '3' },
    { label: 'Damage Types', value: '10+' },
  ];

  const technologies = [
    'PyTorch',
    'YOLO v8',
    'OpenCV',
    'FastAPI',
    'React',
    'PostgreSQL',
    'Docker',
    'TypeScript',
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Hero Section */}
      <Box textAlign="center" mb={6}>
        <Typography variant="h3" component="h1" gutterBottom fontWeight="bold">
          About Car Damage Detection
        </Typography>
        <Typography variant="h6" color="text.secondary" maxWidth="800px" mx="auto">
          Revolutionizing automotive insurance claims in Tunisia with AI-powered damage detection
          and cost estimation technology.
        </Typography>
      </Box>

      {/* Mission Statement */}
      <Paper elevation={2} sx={{ p: 4, mb: 6, bgcolor: 'primary.main', color: 'white' }}>
        <Typography variant="h4" gutterBottom textAlign="center">
          Our Mission
        </Typography>
        <Typography variant="body1" textAlign="center" fontSize="1.1rem" lineHeight={1.6}>
          To streamline the insurance claims process in Tunisia by providing accurate, fast, and
          reliable car damage assessment using cutting-edge artificial intelligence technology.
          We aim to reduce claim processing time, improve accuracy, and enhance customer satisfaction
          for both insurance companies and vehicle owners.
        </Typography>
      </Paper>

      {/* Statistics */}
      <Grid container spacing={3} mb={6}>
        {stats.map((stat, index) => (
          <Grid item xs={6} md={3} key={index}>
            <Card sx={{ textAlign: 'center', p: 2 }}>
              <CardContent>
                <Typography variant="h3" color="primary" fontWeight="bold">
                  {stat.value}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {stat.label}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Features */}
      <Typography variant="h4" gutterBottom textAlign="center" mb={4}>
        Key Features
      </Typography>
      <Grid container spacing={3} mb={6}>
        {features.map((feature, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card sx={{ height: '100%' }}>
              <CardContent sx={{ p: 3 }}>
                <Box display="flex" alignItems="center" mb={2}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    {feature.icon}
                  </Avatar>
                  <Typography variant="h6" fontWeight="bold">
                    {feature.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {feature.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* How It Works */}
      <Paper elevation={1} sx={{ p: 4, mb: 6 }}>
        <Typography variant="h4" gutterBottom textAlign="center">
          How It Works
        </Typography>
        <Grid container spacing={4}>
          <Grid item xs={12} md={3} textAlign="center">
            <Avatar sx={{ bgcolor: 'primary.main', width: 60, height: 60, mx: 'auto', mb: 2 }}>
              1
            </Avatar>
            <Typography variant="h6" gutterBottom>
              Upload Image
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Take a photo of your damaged vehicle and upload it to our platform
            </Typography>
          </Grid>
          <Grid item xs={12} md={3} textAlign="center">
            <Avatar sx={{ bgcolor: 'primary.main', width: 60, height: 60, mx: 'auto', mb: 2 }}>
              2
            </Avatar>
            <Typography variant="h6" gutterBottom>
              AI Analysis
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Our AI algorithms detect and classify different types of damage automatically
            </Typography>
          </Grid>
          <Grid item xs={12} md={3} textAlign="center">
            <Avatar sx={{ bgcolor: 'primary.main', width: 60, height: 60, mx: 'auto', mb: 2 }}>
              3
            </Avatar>
            <Typography variant="h6" gutterBottom>
              Cost Estimation
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Get accurate repair cost estimates based on Tunisian market prices
            </Typography>
          </Grid>
          <Grid item xs={12} md={3} textAlign="center">
            <Avatar sx={{ bgcolor: 'primary.main', width: 60, height: 60, mx: 'auto', mb: 2 }}>
              4
            </Avatar>
            <Typography variant="h6" gutterBottom>
              Generate Report
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Receive a detailed report ready for insurance claim submission
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* Technology Stack */}
      <Typography variant="h4" gutterBottom textAlign="center">
        Technology Stack
      </Typography>
      <Box textAlign="center" mb={6}>
        <Typography variant="body1" color="text.secondary" mb={3}>
          Built with modern, reliable technologies for optimal performance and scalability
        </Typography>
        <Box display="flex" flexWrap="wrap" justifyContent="center" gap={1}>
          {technologies.map((tech, index) => (
            <Chip
              key={index}
              label={tech}
              variant="outlined"
              color="primary"
              sx={{ m: 0.5 }}
            />
          ))}
        </Box>
      </Box>

      <Divider sx={{ my: 4 }} />

      {/* Contact Information */}
      <Paper elevation={1} sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h5" gutterBottom>
          Need Help or Have Questions?
        </Typography>
        <Typography variant="body1" color="text.secondary" mb={3}>
          Our team is here to assist you with any questions about our car damage detection system.
        </Typography>
        <Typography variant="body2" color="text.secondary">
          For technical support or business inquiries, please contact us through our support channels.
        </Typography>
      </Paper>
    </Container>
  );
};

export default AboutPage;
