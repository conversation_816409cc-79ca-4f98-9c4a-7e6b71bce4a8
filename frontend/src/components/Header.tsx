import React from 'react';
import {
  App<PERSON><PERSON>,
  Toolbar,
  Typography,
  Button,
  Box,
  IconButton,
  Menu,
  MenuItem,
} from '@mui/material';
import {
  DirectionsCar,
  Language,
  Menu as MenuIcon,
} from '@mui/icons-material';
import { Link, useLocation } from 'react-router-dom';

const Header: React.FC = () => {
  const location = useLocation();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [langAnchorEl, setLangAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLangMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setLangAnchorEl(event.currentTarget);
  };

  const handleLangMenuClose = () => {
    setLangAnchorEl(null);
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <AppBar position="static" elevation={2}>
      <Toolbar>
        <DirectionsCar sx={{ mr: 2 }} />
        <Typography
          variant="h6"
          component={Link}
          to="/"
          sx={{
            flexGrow: 1,
            textDecoration: 'none',
            color: 'inherit',
            fontWeight: 'bold',
          }}
        >
          Car Damage Detection
        </Typography>

        {/* Desktop Navigation */}
        <Box sx={{ display: { xs: 'none', md: 'flex' }, gap: 2 }}>
          <Button
            color="inherit"
            component={Link}
            to="/"
            sx={{
              backgroundColor: isActive('/') ? 'rgba(255,255,255,0.1)' : 'transparent',
            }}
          >
            Home
          </Button>
          <Button
            color="inherit"
            component={Link}
            to="/reports"
            sx={{
              backgroundColor: isActive('/reports') ? 'rgba(255,255,255,0.1)' : 'transparent',
            }}
          >
            Reports
          </Button>
          <Button
            color="inherit"
            component={Link}
            to="/about"
            sx={{
              backgroundColor: isActive('/about') ? 'rgba(255,255,255,0.1)' : 'transparent',
            }}
          >
            About
          </Button>
          
          {/* Language Selector */}
          <IconButton
            color="inherit"
            onClick={handleLangMenuOpen}
            sx={{ ml: 1 }}
          >
            <Language />
          </IconButton>
        </Box>

        {/* Mobile Navigation */}
        <Box sx={{ display: { xs: 'flex', md: 'none' } }}>
          <IconButton
            color="inherit"
            onClick={handleLangMenuOpen}
            sx={{ mr: 1 }}
          >
            <Language />
          </IconButton>
          <IconButton
            color="inherit"
            onClick={handleMenuOpen}
          >
            <MenuIcon />
          </IconButton>
        </Box>

        {/* Mobile Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          sx={{ display: { xs: 'block', md: 'none' } }}
        >
          <MenuItem onClick={handleMenuClose} component={Link} to="/">
            Home
          </MenuItem>
          <MenuItem onClick={handleMenuClose} component={Link} to="/reports">
            Reports
          </MenuItem>
          <MenuItem onClick={handleMenuClose} component={Link} to="/about">
            About
          </MenuItem>
        </Menu>

        {/* Language Menu */}
        <Menu
          anchorEl={langAnchorEl}
          open={Boolean(langAnchorEl)}
          onClose={handleLangMenuClose}
        >
          <MenuItem onClick={handleLangMenuClose}>
            🇺🇸 English
          </MenuItem>
          <MenuItem onClick={handleLangMenuClose}>
            🇫🇷 Français
          </MenuItem>
          <MenuItem onClick={handleLangMenuClose}>
            🇹🇳 العربية
          </MenuItem>
        </Menu>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
