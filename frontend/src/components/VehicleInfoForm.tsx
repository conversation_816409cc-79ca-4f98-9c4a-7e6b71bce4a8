import React from 'react';
import {
  Box,
  TextField,
  MenuItem,
  Grid,
  Typography,
} from '@mui/material';

interface VehicleInfo {
  make: string;
  model: string;
  year: number;
}

interface VehicleInfoFormProps {
  vehicleInfo: VehicleInfo;
  onChange: (info: VehicleInfo) => void;
}

const VehicleInfoForm: React.FC<VehicleInfoFormProps> = ({ vehicleInfo, onChange }) => {
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 30 }, (_, i) => currentYear - i);

  const carMakes = [
    'Peugeot', 'Renault', 'Citroën', 'Volkswagen', 'Toyota', 'Hyundai',
    'Kia', 'Nissan', 'Ford', 'Opel', 'Fiat', 'Dacia', 'Seat', 'Skoda',
    'BMW', 'Mercedes-Benz', 'Audi', 'Mazda', 'Honda', 'Suzuki', 'Mitsubishi'
  ];

  const handleChange = (field: keyof VehicleInfo) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = field === 'year' ? parseInt(event.target.value) : event.target.value;
    onChange({
      ...vehicleInfo,
      [field]: value,
    });
  };

  return (
    <Box>
      <Typography variant="body2" color="text.secondary" mb={2}>
        Providing vehicle information helps improve cost estimation accuracy
      </Typography>
      
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TextField
            select
            fullWidth
            label="Make"
            value={vehicleInfo.make}
            onChange={handleChange('make')}
            variant="outlined"
          >
            <MenuItem value="">
              <em>Select Make</em>
            </MenuItem>
            {carMakes.map((make) => (
              <MenuItem key={make} value={make}>
                {make}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            label="Model"
            value={vehicleInfo.model}
            onChange={handleChange('model')}
            variant="outlined"
            placeholder="e.g., 208, Clio, Golf"
          />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <TextField
            select
            fullWidth
            label="Year"
            value={vehicleInfo.year}
            onChange={handleChange('year')}
            variant="outlined"
          >
            {years.map((year) => (
              <MenuItem key={year} value={year}>
                {year}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      </Grid>
    </Box>
  );
};

export default VehicleInfoForm;
