import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types
export interface VehicleInfo {
  make?: string;
  model?: string;
  year?: number;
}

export interface DetectedDamage {
  damage_type: string;
  severity: string;
  confidence: number;
  bounding_box: number[];
  affected_area: number;
  description?: string;
}

export interface DamageAnalysisResponse {
  analysis_id: number;
  detected_damages: DetectedDamage[];
  overall_severity: string;
  total_estimated_cost: number;
  confidence_score: number;
  processing_time: number;
  created_at: string;
}

export interface CostBreakdownItem {
  item_name: string;
  item_type: string;
  quantity: number;
  unit_cost: number;
  total_cost: number;
  description?: string;
}

export interface CostBreakdownResponse {
  analysis_id: number;
  parts_cost: number;
  labor_cost: number;
  paint_cost: number;
  tax_amount: number;
  total_cost: number;
  cost_details: CostBreakdownItem[];
  currency: string;
}

export interface InsuranceReportResponse {
  report_id: number;
  report_number: string;
  analysis_id: number;
  vehicle_info: VehicleInfo;
  incident_details: any;
  damage_summary: string;
  repair_recommendations: any[];
  total_cost_estimate: number;
  estimated_repair_time: number;
  status: string;
  created_at: string;
}

// API Functions
export const analyzeDamage = async (
  image: File,
  vehicleInfo?: VehicleInfo
): Promise<DamageAnalysisResponse> => {
  const formData = new FormData();
  formData.append('image', image);
  
  if (vehicleInfo?.make) {
    formData.append('vehicle_make', vehicleInfo.make);
  }
  if (vehicleInfo?.model) {
    formData.append('vehicle_model', vehicleInfo.model);
  }
  if (vehicleInfo?.year) {
    formData.append('vehicle_year', vehicleInfo.year.toString());
  }

  const response = await api.post('/analyze-damage', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};

export const getAnalysis = async (analysisId: number): Promise<DamageAnalysisResponse> => {
  const response = await api.get(`/analysis/${analysisId}`);
  return response.data;
};

export const getCostBreakdown = async (analysisId: number): Promise<CostBreakdownResponse> => {
  const response = await api.get(`/cost-breakdown/${analysisId}`);
  return response.data;
};

export const recalculateCost = async (
  analysisId: number,
  vehicleInfo?: VehicleInfo
): Promise<CostBreakdownResponse> => {
  const response = await api.post(`/recalculate-cost/${analysisId}`, vehicleInfo);
  return response.data;
};

export const generateReport = async (
  analysisId: number,
  vehicleInfo: VehicleInfo,
  incidentDetails: any
): Promise<InsuranceReportResponse> => {
  const response = await api.post('/generate-report', {
    analysis_id: analysisId,
    vehicle_info: vehicleInfo,
    incident_details: incidentDetails,
  });
  return response.data;
};

export const getReport = async (reportId: number): Promise<InsuranceReportResponse> => {
  const response = await api.get(`/report/${reportId}`);
  return response.data;
};

export const listReports = async (
  skip = 0,
  limit = 100,
  status?: string
): Promise<{ reports: any[]; total: number }> => {
  const params = new URLSearchParams({
    skip: skip.toString(),
    limit: limit.toString(),
  });
  
  if (status) {
    params.append('status', status);
  }

  const response = await api.get(`/reports?${params}`);
  return response.data;
};

export const updateReportStatus = async (
  reportId: number,
  status: string
): Promise<{ message: string; report_id: number }> => {
  const response = await api.put(`/report/${reportId}/status`, { status });
  return response.data;
};

export const getCostSummary = async (analysisId: number) => {
  const response = await api.get(`/cost-summary/${analysisId}`);
  return response.data;
};

export const getMarketPrices = async () => {
  const response = await api.get('/market-prices');
  return response.data;
};

// Error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    throw error;
  }
);

export default api;
