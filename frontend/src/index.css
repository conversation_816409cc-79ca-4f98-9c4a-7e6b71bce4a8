body {
  margin: 0;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Loading animation */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Damage detection overlay */
.damage-overlay {
  position: absolute;
  border: 2px solid #ff4444;
  background-color: rgba(255, 68, 68, 0.2);
  pointer-events: none;
}

.damage-label {
  position: absolute;
  background-color: #ff4444;
  color: white;
  padding: 2px 6px;
  font-size: 12px;
  border-radius: 3px;
  top: -20px;
  left: 0;
  white-space: nowrap;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }
  
  .grid-container {
    grid-template-columns: 1fr;
  }
}
