#!/usr/bin/env python3
"""
Auto-annotation script using pre-trained models to generate initial annotations
"""
import cv2
import numpy as np
from pathlib import Path
import argparse
import logging
from typing import List, Tuple, Dict
import json
from ultralytics import YOLO
import torch

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AutoAnnotator:
    """Generate initial annotations using pre-trained models"""
    
    def __init__(self, images_dir: str, output_dir: str):
        self.images_dir = Path(images_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Load pre-trained YOLO model for general object detection
        self.model = YOLO('yolov8n.pt')  # Nano model for speed
        
        # Vehicle damage class mapping (approximate)
        self.damage_mapping = {
            # Map YOLO classes to our damage classes
            'car': None,  # Skip cars themselves
            'truck': None,  # Skip vehicles
            'person': None,  # Skip people
            # We'll use image analysis for actual damage detection
        }
        
    def detect_potential_damage_areas(self, image_path: Path) -> List[Dict]:
        """Detect potential damage areas using image processing"""
        image = cv2.imread(str(image_path))
        if image is None:
            return []
            
        height, width = image.shape[:2]
        detections = []
        
        # Convert to different color spaces for analysis
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Method 1: Edge detection for structural damage
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 500 < area < 50000:  # Filter by size
                x, y, w, h = cv2.boundingRect(contour)
                
                # Convert to YOLO format (normalized)
                x_center = (x + w/2) / width
                y_center = (y + h/2) / height
                norm_width = w / width
                norm_height = h / height
                
                # Classify based on shape and size
                aspect_ratio = w / h if h > 0 else 1
                
                if aspect_ratio > 2:  # Long horizontal damage
                    damage_class = 0  # scratch_surface
                elif area > 10000:  # Large area
                    damage_class = 3  # dent_major
                else:
                    damage_class = 2  # dent_minor
                
                detections.append({
                    'class': damage_class,
                    'confidence': 0.3,  # Low confidence for manual review
                    'bbox': [x_center, y_center, norm_width, norm_height]
                })
        
        # Method 2: Color analysis for paint damage
        # Look for color inconsistencies
        blur = cv2.GaussianBlur(image, (15, 15), 0)
        diff = cv2.absdiff(image, blur)
        gray_diff = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
        
        # Threshold for significant color changes
        _, thresh = cv2.threshold(gray_diff, 30, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 200 < area < 20000:
                x, y, w, h = cv2.boundingRect(contour)
                
                x_center = (x + w/2) / width
                y_center = (y + h/2) / height
                norm_width = w / width
                norm_height = h / height
                
                detections.append({
                    'class': 7,  # paint_damage
                    'confidence': 0.25,
                    'bbox': [x_center, y_center, norm_width, norm_height]
                })
        
        # Method 3: Detect dark areas (potential dents/shadows)
        dark_mask = cv2.inRange(gray, 0, 80)
        contours, _ = cv2.findContours(dark_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 300 < area < 15000:
                x, y, w, h = cv2.boundingRect(contour)
                
                x_center = (x + w/2) / width
                y_center = (y + h/2) / height
                norm_width = w / width
                norm_height = h / height
                
                # Circular shapes more likely to be dents
                aspect_ratio = w / h if h > 0 else 1
                if 0.7 < aspect_ratio < 1.3:  # Roughly circular
                    damage_class = 2 if area < 5000 else 3  # minor or major dent
                else:
                    damage_class = 1  # scratch_deep
                
                detections.append({
                    'class': damage_class,
                    'confidence': 0.2,
                    'bbox': [x_center, y_center, norm_width, norm_height]
                })
        
        return detections
    
    def filter_overlapping_detections(self, detections: List[Dict], iou_threshold: float = 0.5) -> List[Dict]:
        """Remove overlapping detections using Non-Maximum Suppression"""
        if not detections:
            return []
        
        # Sort by confidence
        detections.sort(key=lambda x: x['confidence'], reverse=True)
        
        def calculate_iou(box1, box2):
            """Calculate Intersection over Union"""
            x1_center, y1_center, w1, h1 = box1
            x2_center, y2_center, w2, h2 = box2
            
            # Convert to corner coordinates
            x1_min, y1_min = x1_center - w1/2, y1_center - h1/2
            x1_max, y1_max = x1_center + w1/2, y1_center + h1/2
            x2_min, y2_min = x2_center - w2/2, y2_center - h2/2
            x2_max, y2_max = x2_center + w2/2, y2_center + h2/2
            
            # Calculate intersection
            inter_x_min = max(x1_min, x2_min)
            inter_y_min = max(y1_min, y2_min)
            inter_x_max = min(x1_max, x2_max)
            inter_y_max = min(y1_max, y2_max)
            
            if inter_x_max <= inter_x_min or inter_y_max <= inter_y_min:
                return 0.0
            
            inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min)
            box1_area = w1 * h1
            box2_area = w2 * h2
            union_area = box1_area + box2_area - inter_area
            
            return inter_area / union_area if union_area > 0 else 0.0
        
        filtered = []
        for i, detection in enumerate(detections):
            keep = True
            for j in range(i):
                if calculate_iou(detection['bbox'], detections[j]['bbox']) > iou_threshold:
                    keep = False
                    break
            if keep:
                filtered.append(detection)
        
        return filtered
    
    def save_annotation(self, image_path: Path, detections: List[Dict]):
        """Save detections in YOLO format"""
        annotation_file = self.output_dir / f"{image_path.stem}.txt"
        
        with open(annotation_file, 'w') as f:
            for detection in detections:
                class_id = detection['class']
                bbox = detection['bbox']
                f.write(f"{class_id} {bbox[0]:.6f} {bbox[1]:.6f} {bbox[2]:.6f} {bbox[3]:.6f}\n")
    
    def create_review_html(self, results: Dict):
        """Create HTML file for easy review of auto-annotations"""
        html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>Auto-Annotation Review</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .image-container { margin: 20px 0; border: 1px solid #ccc; padding: 10px; }
        .image-info { background: #f5f5f5; padding: 10px; margin-bottom: 10px; }
        .detection { background: #e8f4f8; padding: 5px; margin: 5px 0; border-left: 3px solid #007acc; }
        .warning { color: #ff6600; font-weight: bold; }
        .stats { background: #f0f8ff; padding: 15px; margin: 20px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🤖 Auto-Annotation Results</h1>
    <div class="stats">
        <h3>📊 Summary</h3>
        <p><strong>Total Images:</strong> {total_images}</p>
        <p><strong>Images with Detections:</strong> {images_with_detections}</p>
        <p><strong>Total Detections:</strong> {total_detections}</p>
        <p><strong>Average per Image:</strong> {avg_per_image:.2f}</p>
    </div>
    
    <div class="warning">
        ⚠️ <strong>Important:</strong> These are automatically generated annotations with low confidence. 
        Please review and correct them manually using LabelImg before training.
    </div>
    
    <h2>🔍 Detection Details</h2>
""".format(
            total_images=results['total_images'],
            images_with_detections=results['images_with_detections'],
            total_detections=results['total_detections'],
            avg_per_image=results['total_detections'] / results['total_images'] if results['total_images'] > 0 else 0
        )
        
        damage_types = [
            "scratch_surface", "scratch_deep", "dent_minor", "dent_major",
            "broken_bumper", "broken_headlight", "broken_mirror", 
            "paint_damage", "glass_damage", "structural_damage"
        ]
        
        for image_name, detections in results['detections'].items():
            html_content += f"""
    <div class="image-container">
        <div class="image-info">
            <strong>📸 {image_name}</strong> - {len(detections)} detections
        </div>
"""
            for detection in detections:
                damage_type = damage_types[detection['class']]
                html_content += f"""
        <div class="detection">
            <strong>{damage_type}</strong> (Class {detection['class']}) - 
            Confidence: {detection['confidence']:.2f} - 
            BBox: [{detection['bbox'][0]:.3f}, {detection['bbox'][1]:.3f}, {detection['bbox'][2]:.3f}, {detection['bbox'][3]:.3f}]
        </div>
"""
            html_content += "    </div>\n"
        
        html_content += """
    <div class="warning">
        <h3>📋 Next Steps:</h3>
        <ol>
            <li>Open LabelImg: <code>python scripts/start_annotation.py</code></li>
            <li>Review each auto-generated annotation</li>
            <li>Correct, add, or remove annotations as needed</li>
            <li>Save in YOLO format</li>
            <li>Proceed with training</li>
        </ol>
    </div>
</body>
</html>
"""
        
        html_file = self.output_dir / "auto_annotation_review.html"
        with open(html_file, 'w') as f:
            f.write(html_content)
        
        logger.info(f"📄 Review file created: {html_file}")
    
    def auto_annotate_dataset(self):
        """Auto-annotate all images in the dataset"""
        logger.info("🤖 Starting auto-annotation process...")
        
        if not self.images_dir.exists():
            raise FileNotFoundError(f"Images directory not found: {self.images_dir}")
        
        image_files = list(self.images_dir.glob("*.jpg"))
        logger.info(f"Found {len(image_files)} images to auto-annotate")
        
        results = {
            'total_images': len(image_files),
            'images_with_detections': 0,
            'total_detections': 0,
            'detections': {}
        }
        
        for i, image_path in enumerate(image_files):
            logger.info(f"Processing {i+1}/{len(image_files)}: {image_path.name}")
            
            # Detect potential damage areas
            detections = self.detect_potential_damage_areas(image_path)
            
            # Filter overlapping detections
            filtered_detections = self.filter_overlapping_detections(detections)
            
            if filtered_detections:
                results['images_with_detections'] += 1
                results['total_detections'] += len(filtered_detections)
                results['detections'][image_path.name] = filtered_detections
                
                # Save annotation file
                self.save_annotation(image_path, filtered_detections)
            else:
                # Create empty annotation file
                annotation_file = self.output_dir / f"{image_path.stem}.txt"
                annotation_file.touch()
        
        # Create review HTML
        self.create_review_html(results)
        
        logger.info("✅ Auto-annotation completed!")
        logger.info(f"📊 Results:")
        logger.info(f"   - Images processed: {results['total_images']}")
        logger.info(f"   - Images with detections: {results['images_with_detections']}")
        logger.info(f"   - Total detections: {results['total_detections']}")
        logger.info(f"   - Average per image: {results['total_detections'] / results['total_images']:.2f}")
        
        logger.info("⚠️  IMPORTANT: These are low-confidence auto-annotations!")
        logger.info("📋 Next steps:")
        logger.info("   1. Review auto_annotation_review.html")
        logger.info("   2. Run: python scripts/start_annotation.py")
        logger.info("   3. Manually review and correct all annotations")
        logger.info("   4. Proceed with training")
        
        return results

def main():
    parser = argparse.ArgumentParser(description="Auto-annotate images for initial annotation")
    parser.add_argument("--images", default="data/training_dataset/processed_images",
                       help="Directory containing images to annotate")
    parser.add_argument("--output", default="data/training_dataset/annotations",
                       help="Output directory for annotations")
    
    args = parser.parse_args()
    
    annotator = AutoAnnotator(args.images, args.output)
    annotator.auto_annotate_dataset()

if __name__ == "__main__":
    main()
