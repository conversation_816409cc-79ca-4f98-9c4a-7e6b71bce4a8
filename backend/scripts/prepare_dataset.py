#!/usr/bin/env python3
"""
Dataset preparation script for vehicle damage detection
"""
import os
import shutil
import random
from pathlib import Path
import argparse
import logging
from typing import List, Tuple
import cv2
import numpy as np
from sklearn.model_selection import train_test_split

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatasetPreparator:
    """Prepare and organize dataset for YOLO training"""
    
    def __init__(self, source_dir: str, output_dir: str):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.train_ratio = 0.7
        self.val_ratio = 0.2
        self.test_ratio = 0.1
        
    def create_directory_structure(self):
        """Create required directory structure"""
        dirs = [
            'training/images', 'training/labels',
            'validation/images', 'validation/labels', 
            'test/images', 'test/labels'
        ]
        
        for dir_name in dirs:
            (self.output_dir / dir_name).mkdir(parents=True, exist_ok=True)
            
        logger.info(f"Created directory structure in {self.output_dir}")
        
    def validate_annotations(self, image_path: Path, label_path: Path) -> bool:
        """Validate YOLO annotation format"""
        try:
            # Check if image exists and is readable
            img = cv2.imread(str(image_path))
            if img is None:
                logger.warning(f"Cannot read image: {image_path}")
                return False
                
            h, w = img.shape[:2]
            
            # Check label file
            if not label_path.exists():
                logger.warning(f"Label file missing: {label_path}")
                return False
                
            with open(label_path, 'r') as f:
                lines = f.readlines()
                
            for line_num, line in enumerate(lines, 1):
                parts = line.strip().split()
                if len(parts) != 5:
                    logger.warning(f"Invalid annotation format in {label_path}:{line_num}")
                    return False
                    
                try:
                    class_id = int(parts[0])
                    x_center, y_center, width, height = map(float, parts[1:])
                    
                    # Validate class ID
                    if class_id < 0 or class_id > 9:  # 10 damage classes (0-9)
                        logger.warning(f"Invalid class ID {class_id} in {label_path}:{line_num}")
                        return False
                        
                    # Validate coordinates (should be normalized 0-1)
                    if not all(0 <= coord <= 1 for coord in [x_center, y_center, width, height]):
                        logger.warning(f"Invalid coordinates in {label_path}:{line_num}")
                        return False
                        
                except ValueError:
                    logger.warning(f"Invalid number format in {label_path}:{line_num}")
                    return False
                    
            return True
            
        except Exception as e:
            logger.error(f"Error validating {image_path}: {e}")
            return False
            
    def get_image_label_pairs(self) -> List[Tuple[Path, Path]]:
        """Get all valid image-label pairs"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        pairs = []
        
        # Find all images in source directory
        for img_path in self.source_dir.rglob("*"):
            if img_path.suffix.lower() in image_extensions:
                # Look for corresponding label file
                label_path = img_path.with_suffix('.txt')
                
                if self.validate_annotations(img_path, label_path):
                    pairs.append((img_path, label_path))
                    
        logger.info(f"Found {len(pairs)} valid image-label pairs")
        return pairs
        
    def split_dataset(self, pairs: List[Tuple[Path, Path]]) -> dict:
        """Split dataset into train/validation/test sets"""
        # First split: train + val vs test
        train_val_pairs, test_pairs = train_test_split(
            pairs, test_size=self.test_ratio, random_state=42
        )
        
        # Second split: train vs val
        val_size = self.val_ratio / (self.train_ratio + self.val_ratio)
        train_pairs, val_pairs = train_test_split(
            train_val_pairs, test_size=val_size, random_state=42
        )
        
        splits = {
            'training': train_pairs,
            'validation': val_pairs,
            'test': test_pairs
        }
        
        logger.info(f"Dataset split - Train: {len(train_pairs)}, "
                   f"Val: {len(val_pairs)}, Test: {len(test_pairs)}")
        
        return splits
        
    def copy_files(self, splits: dict):
        """Copy files to appropriate directories"""
        for split_name, pairs in splits.items():
            img_dir = self.output_dir / split_name / 'images'
            label_dir = self.output_dir / split_name / 'labels'
            
            for img_path, label_path in pairs:
                # Generate new filename to avoid conflicts
                new_name = f"{split_name}_{img_path.stem}"
                
                # Copy image
                new_img_path = img_dir / f"{new_name}{img_path.suffix}"
                shutil.copy2(img_path, new_img_path)
                
                # Copy label
                new_label_path = label_dir / f"{new_name}.txt"
                shutil.copy2(label_path, new_label_path)
                
            logger.info(f"Copied {len(pairs)} files to {split_name} set")
            
    def generate_statistics(self, splits: dict):
        """Generate dataset statistics"""
        stats = {
            'total_images': sum(len(pairs) for pairs in splits.values()),
            'class_distribution': {i: 0 for i in range(10)},  # 10 damage classes
            'splits': {}
        }
        
        for split_name, pairs in splits.items():
            split_stats = {'images': len(pairs), 'annotations': 0}
            
            for _, label_path in pairs:
                with open(label_path, 'r') as f:
                    lines = f.readlines()
                    split_stats['annotations'] += len(lines)
                    
                    for line in lines:
                        class_id = int(line.split()[0])
                        stats['class_distribution'][class_id] += 1
                        
            stats['splits'][split_name] = split_stats
            
        # Save statistics
        stats_file = self.output_dir / 'dataset_stats.txt'
        with open(stats_file, 'w') as f:
            f.write("Dataset Statistics\n")
            f.write("==================\n\n")
            f.write(f"Total Images: {stats['total_images']}\n")
            f.write(f"Total Annotations: {sum(stats['class_distribution'].values())}\n\n")
            
            f.write("Split Distribution:\n")
            for split_name, split_stats in stats['splits'].items():
                f.write(f"  {split_name}: {split_stats['images']} images, "
                       f"{split_stats['annotations']} annotations\n")
                       
            f.write("\nClass Distribution:\n")
            damage_types = [
                "scratch_surface", "scratch_deep", "dent_minor", "dent_major",
                "broken_bumper", "broken_headlight", "broken_mirror", 
                "paint_damage", "glass_damage", "structural_damage"
            ]
            
            for class_id, count in stats['class_distribution'].items():
                f.write(f"  {class_id} ({damage_types[class_id]}): {count}\n")
                
        logger.info(f"Statistics saved to {stats_file}")
        
    def prepare(self):
        """Main preparation function"""
        logger.info("Starting dataset preparation...")
        
        # Create directory structure
        self.create_directory_structure()
        
        # Get all valid image-label pairs
        pairs = self.get_image_label_pairs()
        
        if not pairs:
            raise ValueError("No valid image-label pairs found!")
            
        # Split dataset
        splits = self.split_dataset(pairs)
        
        # Copy files
        self.copy_files(splits)
        
        # Generate statistics
        self.generate_statistics(splits)
        
        logger.info("Dataset preparation completed successfully!")

def main():
    parser = argparse.ArgumentParser(description="Prepare dataset for YOLO training")
    parser.add_argument("--source", required=True, help="Source directory with images and labels")
    parser.add_argument("--output", required=True, help="Output directory for organized dataset")
    parser.add_argument("--train-ratio", type=float, default=0.7, help="Training set ratio")
    parser.add_argument("--val-ratio", type=float, default=0.2, help="Validation set ratio")
    parser.add_argument("--test-ratio", type=float, default=0.1, help="Test set ratio")
    
    args = parser.parse_args()
    
    # Validate ratios
    if abs(args.train_ratio + args.val_ratio + args.test_ratio - 1.0) > 0.001:
        raise ValueError("Train, validation, and test ratios must sum to 1.0")
        
    # Initialize preparator
    preparator = DatasetPreparator(args.source, args.output)
    preparator.train_ratio = args.train_ratio
    preparator.val_ratio = args.val_ratio
    preparator.test_ratio = args.test_ratio
    
    # Prepare dataset
    preparator.prepare()

if __name__ == "__main__":
    main()
