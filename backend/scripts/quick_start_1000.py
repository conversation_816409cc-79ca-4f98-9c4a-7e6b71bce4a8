#!/usr/bin/env python3
"""
Quick start script for training with 1,000 images
"""
import argparse
import logging
import subprocess
import sys
from pathlib import Path
import time
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickStart1000:
    """Quick start pipeline for 1,000 image dataset"""
    
    def __init__(self, raw_data_dir: str):
        self.raw_data_dir = Path(raw_data_dir)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Output directories
        self.prepared_data_dir = Path("data/training_1000")
        self.models_dir = Path("models")
        
    def validate_raw_data(self):
        """Validate that we have approximately 1,000 images"""
        if not self.raw_data_dir.exists():
            raise FileNotFoundError(f"Raw data directory not found: {self.raw_data_dir}")
            
        # Count images
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        image_count = 0
        label_count = 0
        
        for ext in image_extensions:
            image_count += len(list(self.raw_data_dir.rglob(f"*{ext}")))
            image_count += len(list(self.raw_data_dir.rglob(f"*{ext.upper()}")))
            
        label_count = len(list(self.raw_data_dir.rglob("*.txt")))
        
        logger.info(f"Found {image_count} images and {label_count} labels")
        
        # Validation checks
        if image_count < 500:
            raise ValueError(f"Too few images ({image_count}). Need at least 500 for meaningful training.")
        elif image_count < 800:
            logger.warning(f"Dataset is quite small ({image_count} images). Consider collecting more data.")
        elif image_count > 1500:
            logger.info(f"Dataset is larger than expected ({image_count} images). This is good!")
        else:
            logger.info(f"Dataset size is appropriate ({image_count} images)")
            
        if label_count < image_count * 0.8:
            logger.warning(f"Many images lack labels ({label_count}/{image_count}). This will affect training quality.")
            
        return image_count, label_count
        
    def setup_directories(self):
        """Create necessary directories"""
        directories = [
            self.prepared_data_dir,
            self.prepared_data_dir / "training" / "images",
            self.prepared_data_dir / "training" / "labels", 
            self.prepared_data_dir / "validation" / "images",
            self.prepared_data_dir / "validation" / "labels",
            self.models_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
        logger.info("Directory structure created")
        
    def prepare_dataset(self):
        """Prepare dataset with 80/20 train/val split"""
        logger.info("Preparing dataset with 80/20 split...")
        
        cmd = [
            sys.executable, "scripts/prepare_dataset.py",
            "--source", str(self.raw_data_dir),
            "--output", str(self.prepared_data_dir),
            "--train-ratio", "0.8",
            "--val-ratio", "0.2", 
            "--test-ratio", "0.0"  # No test set for small dataset
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Dataset preparation failed: {result.stderr}")
            raise RuntimeError("Dataset preparation failed")
            
        logger.info("Dataset preparation completed")
        
    def run_aggressive_augmentation(self):
        """Run aggressive data augmentation for small dataset"""
        logger.info("Running aggressive data augmentation (5x factor)...")
        
        training_input = self.prepared_data_dir / "training"
        augmented_output = self.prepared_data_dir / "training_augmented"
        
        cmd = [
            sys.executable, "scripts/augment_data.py",
            "--input", str(training_input),
            "--output", str(augmented_output),
            "--factor", "5"  # Aggressive augmentation for small dataset
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Data augmentation failed: {result.stderr}")
            logger.info("Continuing without augmentation...")
            return False
            
        # Replace original training data with augmented data
        import shutil
        if (augmented_output / "images").exists():
            shutil.rmtree(training_input / "images")
            shutil.move(str(augmented_output / "images"), str(training_input / "images"))
            
        if (augmented_output / "labels").exists():
            shutil.rmtree(training_input / "labels")
            shutil.move(str(augmented_output / "labels"), str(training_input / "labels"))
            
        logger.info("Aggressive data augmentation completed")
        return True
        
    def train_optimized_model(self):
        """Train model optimized for small dataset"""
        logger.info("Training model optimized for small dataset...")
        
        cmd = [
            sys.executable, "scripts/train_small_dataset.py",
            "--data", str(self.prepared_data_dir),
            "--model-size", "s"  # Use small model for better performance
        ]
        
        # Run training with real-time output
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                 text=True, bufsize=1, universal_newlines=True)
        
        for line in process.stdout:
            print(line.rstrip())
            
        process.wait()
        
        if process.returncode != 0:
            logger.error("Model training failed")
            raise RuntimeError("Model training failed")
            
        logger.info("Model training completed")
        
    def generate_quick_start_report(self, start_time: float, image_count: int, label_count: int):
        """Generate quick start report"""
        end_time = time.time()
        duration = end_time - start_time
        
        # Find the trained model
        model_files = list(self.models_dir.glob("damage_detection_small_*.pt"))
        latest_model = max(model_files, key=lambda x: x.stat().st_mtime) if model_files else "None"
        
        report_content = f"""
# Quick Start Training Report - 1,000 Image Dataset

## Training Session: {self.timestamp}

### Dataset Summary
- **Total Images**: {image_count}
- **Total Labels**: {label_count}
- **Training Strategy**: Small dataset optimized
- **Training Duration**: {duration:.2f} seconds ({duration/60:.2f} minutes)

### Configuration Used
- **Model**: YOLOv8s (small) with pre-trained weights
- **Epochs**: 200 (increased for small dataset)
- **Batch Size**: 8 (reduced for stability)
- **Learning Rate**: 0.001 (reduced for fine-tuning)
- **Augmentation**: 5x factor (aggressive for small dataset)
- **Data Split**: 80% train, 20% validation

### Expected Performance
- **mAP@0.5**: 0.5-0.6 (50-60% accuracy)
- **Inference Speed**: <100ms per image
- **Deployment Ready**: Yes, for initial testing

### Model Location
- **Trained Model**: {latest_model}
- **Training Logs**: runs/train/small_dataset_{self.timestamp}/

### Next Steps
1. **Test the Model**:
   ```bash
   python -c "
   from app.services.damage_detection import DamageDetectionService
   service = DamageDetectionService()
   damages = service.detect_damages('test_image.jpg')
   print(damages)
   "
   ```

2. **Update Configuration**:
   - Edit `app/core/config.py`
   - Set `DAMAGE_DETECTION_MODEL_PATH = "{latest_model}"`

3. **Deploy and Test**:
   ```bash
   cd backend
   uvicorn app.main:app --reload
   # Test at http://localhost:8000/api/docs
   ```

4. **Collect Feedback**:
   - Test with real images
   - Identify weak areas
   - Collect more data for problematic classes

### Performance Improvement Tips
1. **Collect more data** for underperforming classes
2. **Focus on quality** - remove poor annotations
3. **Add hard examples** - challenging lighting, angles
4. **Iterative training** - retrain with new data
5. **Ensemble methods** - combine multiple models

### Cost Analysis
- **Time Investment**: {duration/3600:.1f} hours
- **Data Collection**: ~2 weeks
- **Annotation**: ~2 weeks  
- **Training**: {duration/60:.1f} minutes
- **Total Project Time**: ~5 weeks

This is a functional model ready for initial deployment and testing!
"""
        
        report_path = Path("models") / f"quick_start_report_{self.timestamp}.md"
        with open(report_path, 'w') as f:
            f.write(report_content)
            
        logger.info(f"Quick start report saved to: {report_path}")
        
    def run_quick_start(self):
        """Run the complete quick start pipeline"""
        start_time = time.time()
        
        try:
            logger.info("🚀 Starting Quick Start Pipeline for 1,000 Image Dataset")
            
            # Step 1: Validate data
            image_count, label_count = self.validate_raw_data()
            
            # Step 2: Setup directories
            self.setup_directories()
            
            # Step 3: Prepare dataset
            self.prepare_dataset()
            
            # Step 4: Aggressive augmentation
            augmentation_success = self.run_aggressive_augmentation()
            
            # Step 5: Train optimized model
            self.train_optimized_model()
            
            # Step 6: Generate report
            self.generate_quick_start_report(start_time, image_count, label_count)
            
            logger.info("✅ Quick Start Pipeline completed successfully!")
            logger.info("🎯 Your model is ready for testing and deployment!")
            
        except Exception as e:
            logger.error(f"❌ Quick Start Pipeline failed: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description="Quick start training for 1,000 image dataset")
    parser.add_argument("--raw-data", required=True, 
                       help="Directory with ~1,000 raw images and labels")
    
    args = parser.parse_args()
    
    # Initialize quick start
    quick_start = QuickStart1000(args.raw_data)
    
    # Run pipeline
    quick_start.run_quick_start()

if __name__ == "__main__":
    main()
