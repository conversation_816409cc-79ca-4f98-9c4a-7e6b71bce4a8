#!/usr/bin/env python3
"""
Training script for vehicle damage detection model using YOLOv8
"""
import argparse
import os
import yaml
from pathlib import Path
import logging
from ultralytics import Y<PERSON><PERSON>
import torch
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DamageDetectionTrainer:
    """Trainer class for vehicle damage detection model"""
    
    def __init__(self, data_path: str, model_size: str = "n"):
        self.data_path = Path(data_path)
        self.model_size = model_size
        self.model_name = f"yolov8{model_size}.pt"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Damage classes mapping
        self.damage_classes = {
            0: "scratch_surface",
            1: "scratch_deep", 
            2: "dent_minor",
            3: "dent_major",
            4: "broken_bumper",
            5: "broken_headlight",
            6: "broken_mirror",
            7: "paint_damage",
            8: "glass_damage",
            9: "structural_damage"
        }
        
    def create_dataset_config(self):
        """Create YOLO dataset configuration file"""
        config = {
            'path': str(self.data_path.absolute()),
            'train': 'training/images',
            'val': 'validation/images',
            'test': 'test/images',
            'nc': len(self.damage_classes),  # number of classes
            'names': list(self.damage_classes.values())
        }
        
        config_path = self.data_path / "dataset.yaml"
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
            
        logger.info(f"Created dataset config: {config_path}")
        return config_path
        
    def validate_dataset(self):
        """Validate dataset structure and files"""
        required_dirs = ['training/images', 'training/labels', 
                        'validation/images', 'validation/labels']
        
        for dir_name in required_dirs:
            dir_path = self.data_path / dir_name
            if not dir_path.exists():
                raise FileNotFoundError(f"Required directory not found: {dir_path}")
                
            # Check if directory has files
            files = list(dir_path.glob("*"))
            if not files:
                logger.warning(f"Directory is empty: {dir_path}")
                
        logger.info("Dataset validation completed")
        
    def train(self, epochs: int = 100, batch_size: int = 16, img_size: int = 640):
        """Train the damage detection model"""
        try:
            # Validate dataset
            self.validate_dataset()
            
            # Create dataset config
            config_path = self.create_dataset_config()
            
            # Initialize model
            model = YOLO(self.model_name)
            logger.info(f"Initialized model: {self.model_name}")
            
            # Training parameters
            train_params = {
                'data': str(config_path),
                'epochs': epochs,
                'batch': batch_size,
                'imgsz': img_size,
                'device': 'cuda' if torch.cuda.is_available() else 'cpu',
                'project': 'runs/train',
                'name': f'damage_detection_{self.timestamp}',
                'save_period': 10,  # Save checkpoint every 10 epochs
                'patience': 20,     # Early stopping patience
                'cache': True,      # Cache images for faster training
                'workers': 8,       # Number of dataloader workers
                'optimizer': 'AdamW',
                'lr0': 0.01,        # Initial learning rate
                'weight_decay': 0.0005,
                'warmup_epochs': 3,
                'box': 7.5,         # Box loss gain
                'cls': 0.5,         # Classification loss gain
                'dfl': 1.5,         # Distribution focal loss gain
                'augment': True,    # Enable augmentation
            }
            
            logger.info("Starting training...")
            logger.info(f"Training parameters: {train_params}")
            
            # Train the model
            results = model.train(**train_params)
            
            # Save the trained model
            model_save_path = Path("models") / f"damage_detection_{self.timestamp}.pt"
            model_save_path.parent.mkdir(exist_ok=True)
            
            # Copy best model to models directory
            best_model_path = Path(f"runs/train/damage_detection_{self.timestamp}/weights/best.pt")
            if best_model_path.exists():
                import shutil
                shutil.copy2(best_model_path, model_save_path)
                logger.info(f"Model saved to: {model_save_path}")
            
            # Update config to point to new model
            self.update_config(model_save_path)
            
            return results
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise
            
    def update_config(self, model_path: Path):
        """Update application config with new model path"""
        config_file = Path("app/core/config.py")
        if config_file.exists():
            logger.info(f"Update DAMAGE_DETECTION_MODEL_PATH in {config_file} to: {model_path}")
        else:
            logger.warning("Config file not found. Manually update DAMAGE_DETECTION_MODEL_PATH")

def main():
    parser = argparse.ArgumentParser(description="Train vehicle damage detection model")
    parser.add_argument("--data", required=True, help="Path to dataset directory")
    parser.add_argument("--epochs", type=int, default=100, help="Number of training epochs")
    parser.add_argument("--batch-size", type=int, default=16, help="Batch size")
    parser.add_argument("--img-size", type=int, default=640, help="Image size")
    parser.add_argument("--model-size", choices=['n', 's', 'm', 'l', 'x'], default='n', 
                       help="YOLOv8 model size (n=nano, s=small, m=medium, l=large, x=xlarge)")
    
    args = parser.parse_args()
    
    # Initialize trainer
    trainer = DamageDetectionTrainer(args.data, args.model_size)
    
    # Start training
    results = trainer.train(
        epochs=args.epochs,
        batch_size=args.batch_size,
        img_size=args.img_size
    )
    
    logger.info("Training completed successfully!")
    logger.info(f"Results: {results}")

if __name__ == "__main__":
    main()
