#!/usr/bin/env python3
"""
Prepare annotated dataset for training by splitting into train/val/test sets
"""
import os
import shutil
import random
from pathlib import Path
import argparse
import logging
from typing import List, Tuple
import json
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AnnotatedDatasetPreparator:
    """Prepare annotated dataset for training"""
    
    def __init__(self, dataset_dir: str = "data/training_dataset"):
        self.dataset_dir = Path(dataset_dir)
        self.images_dir = self.dataset_dir / "processed_images"
        self.annotations_dir = self.dataset_dir / "annotations"
        
        # Split ratios
        self.train_ratio = 0.8
        self.val_ratio = 0.15
        self.test_ratio = 0.05
        
    def validate_annotations(self):
        """Validate that annotations exist and are properly formatted"""
        if not self.images_dir.exists():
            raise FileNotFoundError(f"Images directory not found: {self.images_dir}")
            
        if not self.annotations_dir.exists():
            raise FileNotFoundError(f"Annotations directory not found: {self.annotations_dir}")
        
        # Get all images and annotations
        image_files = list(self.images_dir.glob("*.jpg"))
        annotation_files = list(self.annotations_dir.glob("*.txt"))
        
        logger.info(f"Found {len(image_files)} images and {len(annotation_files)} annotation files")
        
        # Check for matching pairs
        valid_pairs = []
        missing_annotations = []
        
        for img_file in image_files:
            annotation_file = self.annotations_dir / f"{img_file.stem}.txt"
            if annotation_file.exists():
                # Validate annotation format
                if self.validate_annotation_file(annotation_file):
                    valid_pairs.append((img_file, annotation_file))
                else:
                    logger.warning(f"Invalid annotation format: {annotation_file}")
            else:
                missing_annotations.append(img_file)
        
        if missing_annotations:
            logger.warning(f"Missing annotations for {len(missing_annotations)} images")
            logger.info("Images without annotations will be skipped")
        
        if len(valid_pairs) < 100:
            logger.warning(f"Only {len(valid_pairs)} valid pairs found. Consider annotating more images.")
        
        logger.info(f"✅ Found {len(valid_pairs)} valid image-annotation pairs")
        return valid_pairs
    
    def validate_annotation_file(self, annotation_file: Path) -> bool:
        """Validate YOLO annotation file format"""
        try:
            with open(annotation_file, 'r') as f:
                lines = f.readlines()
            
            # Empty files are valid (no damage in image)
            if not lines:
                return True
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:  # Skip empty lines
                    continue
                    
                parts = line.split()
                if len(parts) != 5:
                    logger.warning(f"Invalid format in {annotation_file}:{line_num} - expected 5 values, got {len(parts)}")
                    return False
                
                try:
                    class_id = int(parts[0])
                    x_center, y_center, width, height = map(float, parts[1:])
                    
                    # Validate class ID (0-9 for our damage types)
                    if class_id < 0 or class_id > 9:
                        logger.warning(f"Invalid class ID {class_id} in {annotation_file}:{line_num}")
                        return False
                    
                    # Validate coordinates (should be normalized 0-1)
                    if not all(0 <= coord <= 1 for coord in [x_center, y_center, width, height]):
                        logger.warning(f"Invalid coordinates in {annotation_file}:{line_num}")
                        return False
                        
                except ValueError:
                    logger.warning(f"Invalid number format in {annotation_file}:{line_num}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating {annotation_file}: {e}")
            return False
    
    def split_dataset(self, valid_pairs: List[Tuple[Path, Path]]) -> dict:
        """Split dataset into train/validation/test sets"""
        # Shuffle pairs for random split
        random.seed(42)  # For reproducible splits
        shuffled_pairs = valid_pairs.copy()
        random.shuffle(shuffled_pairs)
        
        total = len(shuffled_pairs)
        train_count = int(total * self.train_ratio)
        val_count = int(total * self.val_ratio)
        
        splits = {
            'train': shuffled_pairs[:train_count],
            'val': shuffled_pairs[train_count:train_count + val_count],
            'test': shuffled_pairs[train_count + val_count:]
        }
        
        logger.info(f"Dataset split:")
        logger.info(f"  Train: {len(splits['train'])} images ({len(splits['train'])/total*100:.1f}%)")
        logger.info(f"  Validation: {len(splits['val'])} images ({len(splits['val'])/total*100:.1f}%)")
        logger.info(f"  Test: {len(splits['test'])} images ({len(splits['test'])/total*100:.1f}%)")
        
        return splits
    
    def copy_split_files(self, splits: dict):
        """Copy files to train/val/test directories"""
        split_mapping = {
            'train': 'training',
            'val': 'validation', 
            'test': 'test'
        }
        
        for split_name, pairs in splits.items():
            target_dir = split_mapping[split_name]
            
            img_target = self.dataset_dir / target_dir / "images"
            label_target = self.dataset_dir / target_dir / "labels"
            
            # Clear existing files
            if img_target.exists():
                shutil.rmtree(img_target)
            if label_target.exists():
                shutil.rmtree(label_target)
                
            img_target.mkdir(parents=True, exist_ok=True)
            label_target.mkdir(parents=True, exist_ok=True)
            
            # Copy files
            for img_file, annotation_file in pairs:
                # Copy image
                shutil.copy2(img_file, img_target / img_file.name)
                
                # Copy annotation
                shutil.copy2(annotation_file, label_target / f"{img_file.stem}.txt")
            
            logger.info(f"✅ Copied {len(pairs)} files to {target_dir} set")
    
    def generate_statistics(self, splits: dict):
        """Generate dataset statistics"""
        damage_types = [
            "scratch_surface", "scratch_deep", "dent_minor", "dent_major",
            "broken_bumper", "broken_headlight", "broken_mirror", 
            "paint_damage", "glass_damage", "structural_damage"
        ]
        
        stats = {
            'total_images': sum(len(pairs) for pairs in splits.values()),
            'total_annotations': 0,
            'class_distribution': {i: 0 for i in range(10)},
            'splits': {}
        }
        
        for split_name, pairs in splits.items():
            split_stats = {
                'images': len(pairs),
                'annotations': 0,
                'class_counts': {i: 0 for i in range(10)}
            }
            
            for img_file, annotation_file in pairs:
                with open(annotation_file, 'r') as f:
                    lines = [line.strip() for line in f.readlines() if line.strip()]
                    split_stats['annotations'] += len(lines)
                    
                    for line in lines:
                        try:
                            class_id = int(line.split()[0])
                            split_stats['class_counts'][class_id] += 1
                            stats['class_distribution'][class_id] += 1
                        except (ValueError, IndexError):
                            continue
            
            stats['splits'][split_name] = split_stats
            stats['total_annotations'] += split_stats['annotations']
        
        # Save statistics report
        report_content = f"""# Annotated Dataset Statistics

## Dataset Summary
- **Total Images**: {stats['total_images']}
- **Total Annotations**: {stats['total_annotations']}
- **Average Annotations per Image**: {stats['total_annotations'] / stats['total_images']:.2f}
- **Preparation Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Split Distribution
"""
        
        for split_name, split_stats in stats['splits'].items():
            report_content += f"### {split_name.title()} Set\n"
            report_content += f"- Images: {split_stats['images']}\n"
            report_content += f"- Annotations: {split_stats['annotations']}\n"
            report_content += f"- Avg per image: {split_stats['annotations'] / split_stats['images']:.2f}\n\n"
        
        report_content += "## Class Distribution\n"
        for class_id, count in stats['class_distribution'].items():
            percentage = (count / stats['total_annotations'] * 100) if stats['total_annotations'] > 0 else 0
            report_content += f"- **{damage_types[class_id]}** (Class {class_id}): {count} ({percentage:.1f}%)\n"
        
        report_content += f"""
## Training Readiness
- ✅ Dataset is ready for training
- 📊 Recommended next steps:
  1. Review class distribution for balance
  2. Run data augmentation if needed
  3. Start training with: `python scripts/train_small_dataset.py --data data/training_dataset`

## Expected Performance
With {stats['total_images']} annotated images:
- **Expected mAP@0.5**: 0.5-0.6 (50-60%)
- **Training Time**: 2-4 hours on GPU
- **Model Size**: ~20MB (YOLOv8s)
"""
        
        stats_file = self.dataset_dir / "dataset_statistics.md"
        with open(stats_file, 'w') as f:
            f.write(report_content)
        
        logger.info(f"📊 Statistics saved to: {stats_file}")
        
        # Also save JSON for programmatic access
        json_stats = {
            'total_images': stats['total_images'],
            'total_annotations': stats['total_annotations'],
            'class_distribution': stats['class_distribution'],
            'splits': {k: {'images': v['images'], 'annotations': v['annotations']} 
                      for k, v in stats['splits'].items()}
        }
        
        json_file = self.dataset_dir / "dataset_statistics.json"
        with open(json_file, 'w') as f:
            json.dump(json_stats, f, indent=2)
        
        return stats
    
    def prepare_dataset(self):
        """Main function to prepare annotated dataset"""
        logger.info("🚀 Preparing annotated dataset for training...")
        
        try:
            # Step 1: Validate annotations
            valid_pairs = self.validate_annotations()
            
            if len(valid_pairs) < 50:
                logger.error("❌ Too few annotated images. Need at least 50 for meaningful training.")
                return False
            
            # Step 2: Split dataset
            splits = self.split_dataset(valid_pairs)
            
            # Step 3: Copy files to split directories
            self.copy_split_files(splits)
            
            # Step 4: Generate statistics
            stats = self.generate_statistics(splits)
            
            logger.info("✅ Dataset preparation completed successfully!")
            logger.info(f"📊 Ready for training with {stats['total_images']} images")
            logger.info(f"🎯 Next step: python scripts/train_small_dataset.py --data data/training_dataset")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Dataset preparation failed: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description="Prepare annotated dataset for training")
    parser.add_argument("--dataset-dir", default="data/training_dataset",
                       help="Dataset directory path")
    
    args = parser.parse_args()
    
    preparator = AnnotatedDatasetPreparator(args.dataset_dir)
    success = preparator.prepare_dataset()
    
    if not success:
        exit(1)

if __name__ == "__main__":
    main()
