#!/usr/bin/env python3
"""
Simple auto-annotation script using computer vision to generate initial annotations
"""
import cv2
import numpy as np
from pathlib import Path
import argparse
import logging
from typing import List, Tuple, Dict
import json
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleAutoAnnotator:
    """Generate initial annotations using basic computer vision"""
    
    def __init__(self, images_dir: str, output_dir: str):
        self.images_dir = Path(images_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Damage type mapping
        self.damage_types = {
            0: "scratch_surface",
            1: "scratch_deep", 
            2: "dent_minor",
            3: "dent_major",
            4: "broken_bumper",
            5: "broken_headlight",
            6: "broken_mirror",
            7: "paint_damage",
            8: "glass_damage",
            9: "structural_damage"
        }
        
    def detect_potential_damage_areas(self, image_path: Path) -> List[Dict]:
        """Detect potential damage areas using basic computer vision"""
        image = cv2.imread(str(image_path))
        if image is None:
            return []
            
        height, width = image.shape[:2]
        detections = []
        
        # Convert to different color spaces
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Method 1: Edge detection for structural damage and scratches
        edges = cv2.Canny(gray, 50, 150)
        
        # Apply morphological operations to connect nearby edges
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 500 < area < 50000:  # Filter by reasonable size
                x, y, w, h = cv2.boundingRect(contour)
                
                # Convert to YOLO format (normalized)
                x_center = (x + w/2) / width
                y_center = (y + h/2) / height
                norm_width = w / width
                norm_height = h / height
                
                # Classify based on shape characteristics
                aspect_ratio = w / h if h > 0 else 1
                
                if aspect_ratio > 3:  # Very long horizontal damage
                    damage_class = 0  # scratch_surface
                    confidence = 0.4
                elif aspect_ratio > 2:  # Long horizontal damage
                    damage_class = 1  # scratch_deep
                    confidence = 0.3
                elif area > 15000:  # Large area damage
                    damage_class = 3  # dent_major
                    confidence = 0.3
                else:
                    damage_class = 2  # dent_minor
                    confidence = 0.25
                
                detections.append({
                    'class': damage_class,
                    'confidence': confidence,
                    'bbox': [x_center, y_center, norm_width, norm_height],
                    'method': 'edge_detection'
                })
        
        # Method 2: Color variance analysis for paint damage
        # Divide image into blocks and analyze color variance
        block_size = 50
        for y in range(0, height - block_size, block_size):
            for x in range(0, width - block_size, block_size):
                block = image[y:y+block_size, x:x+block_size]
                
                # Calculate color variance
                color_var = np.var(block, axis=(0, 1))
                total_var = np.sum(color_var)
                
                # High variance might indicate paint damage
                if total_var > 1000:  # Threshold for significant color variation
                    x_center = (x + block_size/2) / width
                    y_center = (y + block_size/2) / height
                    norm_width = block_size / width
                    norm_height = block_size / height
                    
                    detections.append({
                        'class': 7,  # paint_damage
                        'confidence': min(0.3, total_var / 5000),
                        'bbox': [x_center, y_center, norm_width, norm_height],
                        'method': 'color_variance'
                    })
        
        # Method 3: Dark region detection for dents
        # Dents often appear as darker areas due to shadows
        dark_threshold = np.mean(gray) - np.std(gray)
        dark_mask = gray < dark_threshold
        
        # Apply morphological operations to clean up the mask
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        dark_mask = cv2.morphologyEx(dark_mask.astype(np.uint8), cv2.MORPH_OPEN, kernel)
        
        contours, _ = cv2.findContours(dark_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if 300 < area < 20000:
                x, y, w, h = cv2.boundingRect(contour)
                
                x_center = (x + w/2) / width
                y_center = (y + h/2) / height
                norm_width = w / width
                norm_height = h / height
                
                # Circular shapes more likely to be dents
                aspect_ratio = w / h if h > 0 else 1
                if 0.6 < aspect_ratio < 1.4:  # Roughly circular
                    damage_class = 2 if area < 8000 else 3  # minor or major dent
                    confidence = 0.25
                else:
                    damage_class = 1  # scratch_deep
                    confidence = 0.2
                
                detections.append({
                    'class': damage_class,
                    'confidence': confidence,
                    'bbox': [x_center, y_center, norm_width, norm_height],
                    'method': 'dark_region'
                })
        
        return detections
    
    def filter_overlapping_detections(self, detections: List[Dict], iou_threshold: float = 0.5) -> List[Dict]:
        """Remove overlapping detections using Non-Maximum Suppression"""
        if not detections:
            return []
        
        # Sort by confidence
        detections.sort(key=lambda x: x['confidence'], reverse=True)
        
        def calculate_iou(box1, box2):
            """Calculate Intersection over Union"""
            x1_center, y1_center, w1, h1 = box1
            x2_center, y2_center, w2, h2 = box2
            
            # Convert to corner coordinates
            x1_min, y1_min = x1_center - w1/2, y1_center - h1/2
            x1_max, y1_max = x1_center + w1/2, y1_center + h1/2
            x2_min, y2_min = x2_center - w2/2, y2_center - h2/2
            x2_max, y2_max = x2_center + w2/2, y2_center + h2/2
            
            # Calculate intersection
            inter_x_min = max(x1_min, x2_min)
            inter_y_min = max(y1_min, y2_min)
            inter_x_max = min(x1_max, x2_max)
            inter_y_max = min(y1_max, y2_max)
            
            if inter_x_max <= inter_x_min or inter_y_max <= inter_y_min:
                return 0.0
            
            inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min)
            box1_area = w1 * h1
            box2_area = w2 * h2
            union_area = box1_area + box2_area - inter_area
            
            return inter_area / union_area if union_area > 0 else 0.0
        
        filtered = []
        for i, detection in enumerate(detections):
            keep = True
            for j in range(i):
                if calculate_iou(detection['bbox'], detections[j]['bbox']) > iou_threshold:
                    keep = False
                    break
            if keep:
                filtered.append(detection)
        
        return filtered
    
    def save_annotation(self, image_path: Path, detections: List[Dict]):
        """Save detections in YOLO format"""
        annotation_file = self.output_dir / f"{image_path.stem}.txt"
        
        with open(annotation_file, 'w') as f:
            for detection in detections:
                class_id = detection['class']
                bbox = detection['bbox']
                f.write(f"{class_id} {bbox[0]:.6f} {bbox[1]:.6f} {bbox[2]:.6f} {bbox[3]:.6f}\n")
    
    def auto_annotate_dataset(self):
        """Auto-annotate all images in the dataset"""
        logger.info("🤖 Starting simple auto-annotation process...")
        
        if not self.images_dir.exists():
            raise FileNotFoundError(f"Images directory not found: {self.images_dir}")
        
        image_files = list(self.images_dir.glob("*.jpg"))
        logger.info(f"Found {len(image_files)} images to auto-annotate")
        
        results = {
            'total_images': len(image_files),
            'images_with_detections': 0,
            'total_detections': 0,
            'method_stats': {
                'edge_detection': 0,
                'color_variance': 0,
                'dark_region': 0
            }
        }
        
        for i, image_path in enumerate(image_files):
            if i % 50 == 0:
                logger.info(f"Processing {i+1}/{len(image_files)}: {image_path.name}")
            
            # Detect potential damage areas
            detections = self.detect_potential_damage_areas(image_path)
            
            # Filter overlapping detections
            filtered_detections = self.filter_overlapping_detections(detections)
            
            # Count by method
            for detection in filtered_detections:
                method = detection.get('method', 'unknown')
                if method in results['method_stats']:
                    results['method_stats'][method] += 1
            
            if filtered_detections:
                results['images_with_detections'] += 1
                results['total_detections'] += len(filtered_detections)
                
                # Save annotation file
                self.save_annotation(image_path, filtered_detections)
            else:
                # Create empty annotation file
                annotation_file = self.output_dir / f"{image_path.stem}.txt"
                annotation_file.touch()
        
        logger.info("✅ Simple auto-annotation completed!")
        logger.info(f"📊 Results:")
        logger.info(f"   - Images processed: {results['total_images']}")
        logger.info(f"   - Images with detections: {results['images_with_detections']}")
        logger.info(f"   - Total detections: {results['total_detections']}")
        logger.info(f"   - Average per image: {results['total_detections'] / results['total_images']:.2f}")
        
        logger.info(f"📈 Detection methods:")
        for method, count in results['method_stats'].items():
            logger.info(f"   - {method}: {count} detections")
        
        logger.info("⚠️  IMPORTANT: These are basic auto-annotations with low confidence!")
        logger.info("📋 Next steps:")
        logger.info("   1. Run: python scripts/start_annotation.py")
        logger.info("   2. Review and correct all annotations manually")
        logger.info("   3. Add missing annotations")
        logger.info("   4. Remove false positives")
        
        return results

def main():
    parser = argparse.ArgumentParser(description="Simple auto-annotate images for initial annotation")
    parser.add_argument("--images", default="data/training_dataset/processed_images",
                       help="Directory containing images to annotate")
    parser.add_argument("--output", default="data/training_dataset/annotations",
                       help="Output directory for annotations")
    
    args = parser.parse_args()
    
    annotator = SimpleAutoAnnotator(args.images, args.output)
    annotator.auto_annotate_dataset()

if __name__ == "__main__":
    main()
