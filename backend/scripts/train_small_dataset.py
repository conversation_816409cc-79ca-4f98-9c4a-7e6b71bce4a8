#!/usr/bin/env python3
"""
Optimized training script for small vehicle damage detection dataset (1,000 images)
"""
import argparse
import os
import yaml
from pathlib import Path
import logging
from ultralytics import YOLO
import torch
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SmallDatasetTrainer:
    """Optimized trainer for small datasets"""
    
    def __init__(self, data_path: str, model_size: str = "s"):
        self.data_path = Path(data_path)
        self.model_size = model_size  # Use 's' (small) instead of 'n' (nano) for better performance
        self.model_name = f"yolov8{model_size}.pt"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        self.damage_classes = {
            0: "scratch_surface", 1: "scratch_deep", 2: "dent_minor", 3: "dent_major",
            4: "broken_bumper", 5: "broken_headlight", 6: "broken_mirror", 
            7: "paint_damage", 8: "glass_damage", 9: "structural_damage"
        }
        
    def create_dataset_config(self):
        """Create optimized dataset configuration"""
        config = {
            'path': str(self.data_path.absolute()),
            'train': 'training/images',
            'val': 'validation/images',
            'test': 'test/images',
            'nc': len(self.damage_classes),
            'names': list(self.damage_classes.values())
        }
        
        config_path = self.data_path / "dataset.yaml"
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False)
            
        logger.info(f"Created dataset config: {config_path}")
        return config_path
        
    def get_optimal_hyperparameters(self):
        """Get hyperparameters optimized for small datasets"""
        return {
            # Training settings optimized for small datasets
            'epochs': 200,          # More epochs for small dataset
            'batch': 8,             # Smaller batch size
            'imgsz': 640,           # Standard image size
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            
            # Project settings
            'project': 'runs/train',
            'name': f'small_dataset_{self.timestamp}',
            'save_period': 20,      # Save checkpoint every 20 epochs
            'patience': 50,         # Higher patience for small datasets
            
            # Data loading
            'cache': True,          # Cache images for faster training
            'workers': 4,           # Fewer workers for small dataset
            
            # Optimization
            'optimizer': 'AdamW',
            'lr0': 0.001,           # Lower initial learning rate
            'lrf': 0.01,            # Final learning rate factor
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 5,     # More warmup epochs
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            
            # Loss weights (important for small datasets)
            'box': 7.5,             # Box loss gain
            'cls': 0.5,             # Classification loss gain  
            'dfl': 1.5,             # Distribution focal loss gain
            
            # Augmentation (crucial for small datasets)
            'hsv_h': 0.015,         # Hue augmentation
            'hsv_s': 0.7,           # Saturation augmentation
            'hsv_v': 0.4,           # Value augmentation
            'degrees': 10.0,        # Rotation degrees
            'translate': 0.1,       # Translation fraction
            'scale': 0.5,           # Scaling factor
            'shear': 2.0,           # Shear degrees
            'perspective': 0.0001,  # Perspective transformation
            'flipud': 0.0,          # Vertical flip probability
            'fliplr': 0.5,          # Horizontal flip probability
            'mosaic': 1.0,          # Mosaic augmentation probability
            'mixup': 0.1,           # Mixup augmentation probability
            'copy_paste': 0.1,      # Copy-paste augmentation probability
            
            # Validation
            'val': True,
            'plots': True,          # Generate training plots
            'save_json': True,      # Save results in JSON format
        }
        
    def validate_small_dataset(self):
        """Validate dataset structure for small dataset training"""
        required_dirs = ['training/images', 'training/labels', 
                        'validation/images', 'validation/labels']
        
        total_images = 0
        class_counts = {i: 0 for i in range(10)}
        
        for dir_name in required_dirs:
            dir_path = self.data_path / dir_name
            if not dir_path.exists():
                raise FileNotFoundError(f"Required directory not found: {dir_path}")
                
            if 'images' in dir_name:
                images = list(dir_path.glob("*.jpg")) + list(dir_path.glob("*.png"))
                total_images += len(images)
                logger.info(f"{dir_name}: {len(images)} images")
                
            elif 'labels' in dir_name:
                labels = list(dir_path.glob("*.txt"))
                # Count class distribution
                for label_file in labels:
                    with open(label_file, 'r') as f:
                        for line in f:
                            class_id = int(line.split()[0])
                            class_counts[class_id] += 1
                            
        logger.info(f"Total images: {total_images}")
        logger.info("Class distribution:")
        for class_id, count in class_counts.items():
            class_name = self.damage_classes[class_id]
            logger.info(f"  {class_id} ({class_name}): {count} instances")
            
        # Warnings for small dataset
        if total_images < 800:
            logger.warning(f"Dataset is quite small ({total_images} images). Consider data augmentation.")
            
        min_class_count = min(class_counts.values())
        if min_class_count < 20:
            logger.warning(f"Some classes have very few examples (min: {min_class_count}). This may affect performance.")
            
        return total_images, class_counts
        
    def train(self):
        """Train model optimized for small dataset"""
        try:
            # Validate dataset
            total_images, class_counts = self.validate_small_dataset()
            
            # Create dataset config
            config_path = self.create_dataset_config()
            
            # Initialize model with pre-trained weights (crucial for small datasets)
            model = YOLO(self.model_name)
            logger.info(f"Initialized model: {self.model_name} (pre-trained weights)")
            
            # Get optimized hyperparameters
            train_params = self.get_optimal_hyperparameters()
            train_params['data'] = str(config_path)
            
            logger.info("Starting training with small dataset optimizations...")
            logger.info(f"Key optimizations:")
            logger.info(f"  - Higher epochs: {train_params['epochs']}")
            logger.info(f"  - Smaller batch size: {train_params['batch']}")
            logger.info(f"  - Lower learning rate: {train_params['lr0']}")
            logger.info(f"  - Enhanced augmentation enabled")
            logger.info(f"  - Pre-trained weights: {self.model_name}")
            
            # Train the model
            results = model.train(**train_params)
            
            # Save the trained model
            model_save_path = Path("models") / f"damage_detection_small_{self.timestamp}.pt"
            model_save_path.parent.mkdir(exist_ok=True)
            
            # Copy best model
            best_model_path = Path(f"runs/train/small_dataset_{self.timestamp}/weights/best.pt")
            if best_model_path.exists():
                import shutil
                shutil.copy2(best_model_path, model_save_path)
                logger.info(f"Model saved to: {model_save_path}")
                
                # Also save the last model for comparison
                last_model_path = Path(f"runs/train/small_dataset_{self.timestamp}/weights/last.pt")
                if last_model_path.exists():
                    last_save_path = Path("models") / f"damage_detection_small_last_{self.timestamp}.pt"
                    shutil.copy2(last_model_path, last_save_path)
                    logger.info(f"Last model saved to: {last_save_path}")
            
            # Generate training summary
            self.generate_training_summary(total_images, class_counts, results)
            
            return results
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise
            
    def generate_training_summary(self, total_images, class_counts, results):
        """Generate training summary for small dataset"""
        summary = f"""
# Small Dataset Training Summary - {self.timestamp}

## Dataset Statistics
- Total Images: {total_images}
- Model Size: YOLOv8{self.model_size}
- Training Strategy: Small dataset optimized

## Class Distribution
"""
        for class_id, count in class_counts.items():
            class_name = self.damage_classes[class_id]
            summary += f"- {class_name}: {count} instances\n"
            
        summary += f"""
## Training Configuration
- Epochs: 200 (increased for small dataset)
- Batch Size: 8 (reduced for stability)
- Learning Rate: 0.001 (reduced for fine-tuning)
- Augmentation: Enhanced (crucial for small datasets)
- Pre-trained Weights: Yes (transfer learning)

## Recommendations for Improvement
1. Collect more data if possible (target: 2,000+ images)
2. Focus on underrepresented classes
3. Use data augmentation extensively
4. Consider synthetic data generation
5. Implement active learning for efficient annotation

## Next Steps
1. Test model on validation set
2. Evaluate performance metrics
3. Deploy for initial testing
4. Collect feedback and more data
"""
        
        summary_path = Path("models") / f"training_summary_small_{self.timestamp}.md"
        with open(summary_path, 'w') as f:
            f.write(summary)
            
        logger.info(f"Training summary saved to: {summary_path}")

def main():
    parser = argparse.ArgumentParser(description="Train model optimized for small dataset")
    parser.add_argument("--data", required=True, help="Path to dataset directory")
    parser.add_argument("--model-size", choices=['n', 's', 'm'], default='s', 
                       help="Model size (s recommended for small datasets)")
    
    args = parser.parse_args()
    
    # Initialize trainer
    trainer = SmallDatasetTrainer(args.data, args.model_size)
    
    # Start training
    results = trainer.train()
    
    logger.info("Small dataset training completed successfully!")

if __name__ == "__main__":
    main()
