#!/usr/bin/env python3
"""
Complete training pipeline for vehicle damage detection model
"""
import argparse
import logging
import subprocess
import sys
from pathlib import Path
import shutil
import time
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingPipeline:
    """Complete training pipeline orchestrator"""
    
    def __init__(self, raw_data_dir: str, output_dir: str = "training_output"):
        self.raw_data_dir = Path(raw_data_dir)
        self.output_dir = Path(output_dir)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Pipeline directories
        self.prepared_data_dir = self.output_dir / "prepared_dataset"
        self.augmented_data_dir = self.output_dir / "augmented_dataset"
        self.models_dir = Path("models")
        
    def setup_directories(self):
        """Create necessary directories"""
        directories = [
            self.output_dir,
            self.prepared_data_dir,
            self.augmented_data_dir,
            self.models_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
        logger.info("Directory structure created")
        
    def validate_raw_data(self):
        """Validate raw data directory"""
        if not self.raw_data_dir.exists():
            raise FileNotFoundError(f"Raw data directory not found: {self.raw_data_dir}")
            
        # Check for images and labels
        image_files = list(self.raw_data_dir.rglob("*.jpg")) + \
                     list(self.raw_data_dir.rglob("*.jpeg")) + \
                     list(self.raw_data_dir.rglob("*.png"))
                     
        label_files = list(self.raw_data_dir.rglob("*.txt"))
        
        logger.info(f"Found {len(image_files)} images and {len(label_files)} label files")
        
        if len(image_files) == 0:
            raise ValueError("No image files found in raw data directory")
            
        if len(label_files) == 0:
            logger.warning("No label files found - training will not be possible")
            
        return len(image_files), len(label_files)
        
    def run_data_preparation(self):
        """Run dataset preparation script"""
        logger.info("Starting data preparation...")
        
        cmd = [
            sys.executable, "scripts/prepare_dataset.py",
            "--source", str(self.raw_data_dir),
            "--output", str(self.prepared_data_dir)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Data preparation failed: {result.stderr}")
            raise RuntimeError("Data preparation failed")
            
        logger.info("Data preparation completed successfully")
        
    def run_data_augmentation(self, augment_factor: int = 3):
        """Run data augmentation"""
        logger.info(f"Starting data augmentation (factor: {augment_factor})...")
        
        # Augment training data only
        training_input = self.prepared_data_dir / "training"
        training_output = self.augmented_data_dir / "training"
        
        cmd = [
            sys.executable, "scripts/augment_data.py",
            "--input", str(training_input),
            "--output", str(training_output),
            "--factor", str(augment_factor)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Data augmentation failed: {result.stderr}")
            raise RuntimeError("Data augmentation failed")
            
        # Copy validation and test data without augmentation
        for split in ["validation", "test"]:
            src = self.prepared_data_dir / split
            dst = self.augmented_data_dir / split
            if src.exists():
                shutil.copytree(src, dst, dirs_exist_ok=True)
                
        logger.info("Data augmentation completed successfully")
        
    def run_training(self, epochs: int = 100, batch_size: int = 16, model_size: str = "n"):
        """Run model training"""
        logger.info(f"Starting model training (epochs: {epochs}, batch: {batch_size})...")
        
        cmd = [
            sys.executable, "scripts/train_model.py",
            "--data", str(self.augmented_data_dir),
            "--epochs", str(epochs),
            "--batch-size", str(batch_size),
            "--model-size", model_size
        ]
        
        # Run training with real-time output
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                 text=True, bufsize=1, universal_newlines=True)
        
        # Stream output in real-time
        for line in process.stdout:
            print(line.rstrip())
            
        process.wait()
        
        if process.returncode != 0:
            logger.error("Model training failed")
            raise RuntimeError("Model training failed")
            
        logger.info("Model training completed successfully")
        
    def evaluate_model(self):
        """Evaluate trained model"""
        logger.info("Starting model evaluation...")
        
        # Find the latest trained model
        model_files = list(self.models_dir.glob("damage_detection_*.pt"))
        if not model_files:
            logger.error("No trained model found for evaluation")
            return
            
        latest_model = max(model_files, key=lambda x: x.stat().st_mtime)
        logger.info(f"Evaluating model: {latest_model}")
        
        # Run evaluation (this would be implemented based on your evaluation needs)
        # For now, just log that evaluation would happen here
        logger.info("Model evaluation completed (implement specific evaluation logic)")
        
    def generate_report(self, start_time: float):
        """Generate training report"""
        end_time = time.time()
        duration = end_time - start_time
        
        report_content = f"""
# Vehicle Damage Detection Model Training Report

## Training Session: {self.timestamp}

### Configuration
- Raw Data Directory: {self.raw_data_dir}
- Output Directory: {self.output_dir}
- Training Duration: {duration:.2f} seconds ({duration/60:.2f} minutes)

### Dataset Statistics
- Prepared Dataset: {self.prepared_data_dir}
- Augmented Dataset: {self.augmented_data_dir}

### Model Output
- Models Directory: {self.models_dir}
- Latest Model: {max(self.models_dir.glob("damage_detection_*.pt"), key=lambda x: x.stat().st_mtime, default="None")}

### Next Steps
1. Test the model with sample images
2. Deploy to production environment
3. Monitor model performance
4. Collect feedback for future improvements

### Files Generated
- Prepared dataset in: {self.prepared_data_dir}
- Augmented dataset in: {self.augmented_data_dir}
- Training logs in: runs/train/
- Model files in: {self.models_dir}
"""
        
        report_path = self.output_dir / f"training_report_{self.timestamp}.md"
        with open(report_path, 'w') as f:
            f.write(report_content)
            
        logger.info(f"Training report saved to: {report_path}")
        
    def run_full_pipeline(self, epochs: int = 100, batch_size: int = 16, 
                         model_size: str = "n", augment_factor: int = 3):
        """Run the complete training pipeline"""
        start_time = time.time()
        
        try:
            logger.info("Starting complete training pipeline...")
            
            # Step 1: Setup
            self.setup_directories()
            
            # Step 2: Validate raw data
            img_count, label_count = self.validate_raw_data()
            
            # Step 3: Prepare dataset
            self.run_data_preparation()
            
            # Step 4: Augment data
            self.run_data_augmentation(augment_factor)
            
            # Step 5: Train model
            self.run_training(epochs, batch_size, model_size)
            
            # Step 6: Evaluate model
            self.evaluate_model()
            
            # Step 7: Generate report
            self.generate_report(start_time)
            
            logger.info("Complete training pipeline finished successfully!")
            
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description="Complete vehicle damage detection training pipeline")
    parser.add_argument("--raw-data", required=True, help="Directory with raw images and labels")
    parser.add_argument("--output", default="training_output", help="Output directory")
    parser.add_argument("--epochs", type=int, default=100, help="Training epochs")
    parser.add_argument("--batch-size", type=int, default=16, help="Batch size")
    parser.add_argument("--model-size", choices=['n', 's', 'm', 'l', 'x'], default='n', 
                       help="YOLOv8 model size")
    parser.add_argument("--augment-factor", type=int, default=3, 
                       help="Data augmentation factor")
    
    args = parser.parse_args()
    
    # Initialize pipeline
    pipeline = TrainingPipeline(args.raw_data, args.output)
    
    # Run complete pipeline
    pipeline.run_full_pipeline(
        epochs=args.epochs,
        batch_size=args.batch_size,
        model_size=args.model_size,
        augment_factor=args.augment_factor
    )

if __name__ == "__main__":
    main()
