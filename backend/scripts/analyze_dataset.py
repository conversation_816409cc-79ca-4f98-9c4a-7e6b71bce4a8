#!/usr/bin/env python3
"""
Dataset Analysis Script for Vehicle Damage Detection

This script analyzes your dataset to understand:
1. Class distribution and balance
2. Image quality and consistency
3. Potential training challenges
4. Data preprocessing requirements

Run: python scripts/analyze_dataset.py
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
try:
    import seaborn as sns
    HAS_SEABORN = True
except ImportError:
    HAS_SEABORN = False
    print("Warning: seaborn not installed. Using matplotlib only.")

try:
    import cv2
    HAS_OPENCV = True
except ImportError:
    HAS_OPENCV = False
    print("Warning: opencv-python not installed. Skipping advanced image analysis.")

from PIL import Image
import json
from collections import Counter
import argparse
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatasetAnalyzer:
    """Comprehensive dataset analysis for vehicle damage detection"""
    
    def __init__(self, csv_path="data/data.csv", images_dir="data/image"):
        # Handle relative paths - look for data directory in current or parent directory
        if not Path(csv_path).exists() and Path("../data/data.csv").exists():
            csv_path = "../data/data.csv"
            images_dir = "../data/image"

        self.csv_path = Path(csv_path)
        self.images_dir = Path(images_dir)
        self.df = None
        self.analysis_results = {}

        # Create output directory relative to data directory
        data_parent = self.csv_path.parent
        self.output_dir = data_parent / "analysis"
        # Create parent directories if they don't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def load_dataset(self):
        """Load and validate the dataset"""
        logger.info("Loading dataset...")
        
        if not self.csv_path.exists():
            raise FileNotFoundError(f"CSV file not found: {self.csv_path}")
        
        if not self.images_dir.exists():
            raise FileNotFoundError(f"Images directory not found: {self.images_dir}")
        
        # Load CSV
        self.df = pd.read_csv(self.csv_path)
        logger.info(f"Loaded {len(self.df)} records from CSV")
        
        # Basic validation
        required_columns = ['image', 'classes']
        missing_cols = [col for col in required_columns if col not in self.df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        return self.df
    
    def analyze_class_distribution(self):
        """Analyze the distribution of damage classes"""
        logger.info("Analyzing class distribution...")
        
        class_counts = self.df['classes'].value_counts()
        class_percentages = (class_counts / len(self.df) * 100).round(2)
        
        # Store results
        self.analysis_results['class_distribution'] = {
            'counts': class_counts.to_dict(),
            'percentages': class_percentages.to_dict(),
            'total_images': len(self.df),
            'unique_classes': len(class_counts)
        }
        
        # Create visualization
        plt.figure(figsize=(12, 8))
        
        # Bar plot
        plt.subplot(2, 2, 1)
        class_counts.plot(kind='bar', color='skyblue')
        plt.title('Class Distribution (Count)')
        plt.xlabel('Damage Class')
        plt.ylabel('Number of Images')
        plt.xticks(rotation=45)
        
        # Pie chart
        plt.subplot(2, 2, 2)
        plt.pie(class_counts.values, labels=class_counts.index, autopct='%1.1f%%')
        plt.title('Class Distribution (Percentage)')
        
        # Class balance analysis
        plt.subplot(2, 2, 3)
        imbalance_ratio = class_counts.max() / class_counts.min()
        plt.bar(['Max Class', 'Min Class', 'Imbalance Ratio'], 
                [class_counts.max(), class_counts.min(), imbalance_ratio])
        plt.title(f'Class Imbalance Analysis\nRatio: {imbalance_ratio:.2f}:1')
        plt.ylabel('Count / Ratio')
        
        # Recommendations
        plt.subplot(2, 2, 4)
        plt.axis('off')
        recommendations = self._get_class_balance_recommendations(class_counts)
        plt.text(0.1, 0.9, "Recommendations:", fontsize=12, fontweight='bold')
        for i, rec in enumerate(recommendations):
            plt.text(0.1, 0.8 - i*0.15, f"• {rec}", fontsize=10, wrap=True)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'class_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        return self.analysis_results['class_distribution']
    
    def analyze_image_properties(self, sample_size=100):
        """Analyze image properties like size, quality, format"""
        logger.info(f"Analyzing image properties (sample size: {sample_size})...")
        
        # Sample images for analysis (to avoid processing all 1595 images)
        sample_df = self.df.sample(n=min(sample_size, len(self.df)), random_state=42)
        
        image_properties = {
            'widths': [],
            'heights': [],
            'file_sizes': [],
            'aspect_ratios': [],
            'missing_images': [],
            'corrupted_images': []
        }
        
        for idx, row in sample_df.iterrows():
            image_path = self.images_dir / row['image']
            
            if not image_path.exists():
                image_properties['missing_images'].append(row['image'])
                continue
            
            try:
                # Get file size
                file_size = image_path.stat().st_size / 1024  # KB
                image_properties['file_sizes'].append(file_size)
                
                # Get image dimensions
                with Image.open(image_path) as img:
                    width, height = img.size
                    image_properties['widths'].append(width)
                    image_properties['heights'].append(height)
                    image_properties['aspect_ratios'].append(width / height)
                    
            except Exception as e:
                logger.warning(f"Error processing {image_path}: {e}")
                image_properties['corrupted_images'].append(row['image'])
        
        # Calculate statistics
        stats = {}
        for prop in ['widths', 'heights', 'file_sizes', 'aspect_ratios']:
            if image_properties[prop]:
                stats[prop] = {
                    'mean': np.mean(image_properties[prop]),
                    'std': np.std(image_properties[prop]),
                    'min': np.min(image_properties[prop]),
                    'max': np.max(image_properties[prop]),
                    'median': np.median(image_properties[prop])
                }
        
        self.analysis_results['image_properties'] = {
            'statistics': stats,
            'missing_count': len(image_properties['missing_images']),
            'corrupted_count': len(image_properties['corrupted_images']),
            'sample_size': len(sample_df),
            'missing_images': image_properties['missing_images'][:10],  # First 10
            'corrupted_images': image_properties['corrupted_images'][:10]
        }
        
        # Create visualizations
        if image_properties['widths']:
            plt.figure(figsize=(15, 10))
            
            # Image dimensions scatter plot
            plt.subplot(2, 3, 1)
            plt.scatter(image_properties['widths'], image_properties['heights'], alpha=0.6)
            plt.xlabel('Width (pixels)')
            plt.ylabel('Height (pixels)')
            plt.title('Image Dimensions Distribution')
            
            # Width distribution
            plt.subplot(2, 3, 2)
            plt.hist(image_properties['widths'], bins=20, alpha=0.7, color='blue')
            plt.xlabel('Width (pixels)')
            plt.ylabel('Frequency')
            plt.title('Width Distribution')
            
            # Height distribution
            plt.subplot(2, 3, 3)
            plt.hist(image_properties['heights'], bins=20, alpha=0.7, color='green')
            plt.xlabel('Height (pixels)')
            plt.ylabel('Frequency')
            plt.title('Height Distribution')
            
            # File size distribution
            plt.subplot(2, 3, 4)
            plt.hist(image_properties['file_sizes'], bins=20, alpha=0.7, color='orange')
            plt.xlabel('File Size (KB)')
            plt.ylabel('Frequency')
            plt.title('File Size Distribution')
            
            # Aspect ratio distribution
            plt.subplot(2, 3, 5)
            plt.hist(image_properties['aspect_ratios'], bins=20, alpha=0.7, color='red')
            plt.xlabel('Aspect Ratio (W/H)')
            plt.ylabel('Frequency')
            plt.title('Aspect Ratio Distribution')
            
            # Summary statistics
            plt.subplot(2, 3, 6)
            plt.axis('off')
            summary_text = f"""
            Sample Size: {len(sample_df)}
            Missing Images: {len(image_properties['missing_images'])}
            Corrupted Images: {len(image_properties['corrupted_images'])}
            
            Average Dimensions:
            {stats['widths']['mean']:.0f} x {stats['heights']['mean']:.0f} px
            
            Average File Size:
            {stats['file_sizes']['mean']:.1f} KB
            
            Common Aspect Ratio:
            {stats['aspect_ratios']['median']:.2f}:1
            """
            plt.text(0.1, 0.9, summary_text, fontsize=10, verticalalignment='top')
            
            plt.tight_layout()
            plt.savefig(self.output_dir / 'image_properties.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        return self.analysis_results['image_properties']
    
    def analyze_training_readiness(self):
        """Analyze dataset readiness for training"""
        logger.info("Analyzing training readiness...")
        
        class_counts = self.df['classes'].value_counts()
        
        # Training readiness criteria
        readiness_score = 0
        max_score = 100
        issues = []
        recommendations = []
        
        # 1. Minimum samples per class (20 points)
        min_samples = class_counts.min()
        if min_samples >= 100:
            readiness_score += 20
        elif min_samples >= 50:
            readiness_score += 15
            issues.append(f"Some classes have few samples (min: {min_samples})")
            recommendations.append("Consider data augmentation for minority classes")
        else:
            readiness_score += 5
            issues.append(f"Very few samples in some classes (min: {min_samples})")
            recommendations.append("Collect more data or use heavy augmentation")
        
        # 2. Class balance (20 points)
        imbalance_ratio = class_counts.max() / class_counts.min()
        if imbalance_ratio <= 2:
            readiness_score += 20
        elif imbalance_ratio <= 5:
            readiness_score += 15
            issues.append(f"Moderate class imbalance (ratio: {imbalance_ratio:.1f}:1)")
            recommendations.append("Use weighted loss function or class balancing")
        else:
            readiness_score += 5
            issues.append(f"High class imbalance (ratio: {imbalance_ratio:.1f}:1)")
            recommendations.append("Apply oversampling or weighted loss")
        
        # 3. Total dataset size (20 points)
        total_samples = len(self.df)
        if total_samples >= 1000:
            readiness_score += 20
        elif total_samples >= 500:
            readiness_score += 15
        else:
            readiness_score += 10
            issues.append(f"Small dataset size ({total_samples} samples)")
            recommendations.append("Consider transfer learning and data augmentation")
        
        # 4. Number of classes (20 points)
        num_classes = len(class_counts)
        if 2 <= num_classes <= 10:
            readiness_score += 20
        elif num_classes <= 20:
            readiness_score += 15
        else:
            readiness_score += 10
            issues.append(f"Many classes ({num_classes}) may be challenging")
            recommendations.append("Consider grouping similar classes")
        
        # 5. Data quality (20 points)
        if 'image_properties' in self.analysis_results:
            missing_rate = (self.analysis_results['image_properties']['missing_count'] / 
                          self.analysis_results['image_properties']['sample_size'])
            if missing_rate == 0:
                readiness_score += 20
            elif missing_rate < 0.05:
                readiness_score += 15
            else:
                readiness_score += 10
                issues.append(f"Missing images detected ({missing_rate:.1%})")
                recommendations.append("Clean dataset and fix missing images")
        else:
            readiness_score += 15  # Assume good quality if not analyzed
        
        # Determine readiness level
        if readiness_score >= 90:
            readiness_level = "Excellent"
        elif readiness_score >= 75:
            readiness_level = "Good"
        elif readiness_score >= 60:
            readiness_level = "Fair"
        else:
            readiness_level = "Poor"
        
        self.analysis_results['training_readiness'] = {
            'score': readiness_score,
            'max_score': max_score,
            'level': readiness_level,
            'issues': issues,
            'recommendations': recommendations
        }
        
        return self.analysis_results['training_readiness']
    
    def _get_class_balance_recommendations(self, class_counts):
        """Get recommendations for handling class imbalance"""
        imbalance_ratio = class_counts.max() / class_counts.min()
        recommendations = []
        
        if imbalance_ratio > 5:
            recommendations.append("High imbalance detected - use weighted loss")
            recommendations.append("Apply oversampling to minority classes")
            recommendations.append("Consider SMOTE or similar techniques")
        elif imbalance_ratio > 2:
            recommendations.append("Moderate imbalance - use class weights")
            recommendations.append("Apply data augmentation to smaller classes")
        else:
            recommendations.append("Good class balance for training")
        
        return recommendations
    
    def generate_report(self):
        """Generate comprehensive analysis report"""
        logger.info("Generating analysis report...")
        
        report = {
            'analysis_date': datetime.now().isoformat(),
            'dataset_path': str(self.csv_path),
            'images_path': str(self.images_dir),
            'results': self.analysis_results
        }
        
        # Save JSON report
        with open(self.output_dir / 'analysis_report.json', 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        # Generate markdown report
        self._generate_markdown_report()
        
        logger.info(f"Analysis complete! Results saved to {self.output_dir}")
        return report
    
    def _generate_markdown_report(self):
        """Generate human-readable markdown report"""
        md_content = f"""# Dataset Analysis Report

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Dataset Overview
- **Total Images**: {self.analysis_results['class_distribution']['total_images']}
- **Unique Classes**: {self.analysis_results['class_distribution']['unique_classes']}
- **Dataset Path**: {self.csv_path}
- **Images Path**: {self.images_dir}

## Class Distribution
"""
        
        # Add class distribution table
        if 'class_distribution' in self.analysis_results:
            md_content += "\n| Class | Count | Percentage |\n|-------|-------|------------|\n"
            for class_name, count in self.analysis_results['class_distribution']['counts'].items():
                percentage = self.analysis_results['class_distribution']['percentages'][class_name]
                md_content += f"| {class_name} | {count} | {percentage}% |\n"
        
        # Add training readiness
        if 'training_readiness' in self.analysis_results:
            readiness = self.analysis_results['training_readiness']
            md_content += f"""
## Training Readiness
- **Score**: {readiness['score']}/{readiness['max_score']} ({readiness['level']})

### Issues Identified:
"""
            for issue in readiness['issues']:
                md_content += f"- {issue}\n"
            
            md_content += "\n### Recommendations:\n"
            for rec in readiness['recommendations']:
                md_content += f"- {rec}\n"
        
        # Save markdown report
        with open(self.output_dir / 'analysis_report.md', 'w') as f:
            f.write(md_content)
    
    def run_full_analysis(self):
        """Run complete dataset analysis"""
        logger.info("Starting comprehensive dataset analysis...")
        
        # Load dataset
        self.load_dataset()
        
        # Run all analyses
        self.analyze_class_distribution()
        self.analyze_image_properties()
        self.analyze_training_readiness()
        
        # Generate report
        report = self.generate_report()
        
        # Print summary
        self._print_summary()
        
        return report
    
    def _print_summary(self):
        """Print analysis summary to console"""
        print("\n" + "="*60)
        print("📊 DATASET ANALYSIS SUMMARY")
        print("="*60)
        
        if 'class_distribution' in self.analysis_results:
            cd = self.analysis_results['class_distribution']
            print(f"📁 Total Images: {cd['total_images']}")
            print(f"🏷️  Classes: {cd['unique_classes']}")
            print(f"📈 Most Common: {max(cd['counts'], key=cd['counts'].get)} ({max(cd['counts'].values())} images)")
            print(f"📉 Least Common: {min(cd['counts'], key=cd['counts'].get)} ({min(cd['counts'].values())} images)")
        
        if 'training_readiness' in self.analysis_results:
            tr = self.analysis_results['training_readiness']
            print(f"🎯 Training Readiness: {tr['level']} ({tr['score']}/{tr['max_score']})")
        
        print(f"📄 Full report saved to: {self.output_dir}")
        print("="*60)

def main():
    parser = argparse.ArgumentParser(description="Analyze vehicle damage dataset")
    parser.add_argument("--csv", default="data/data.csv", help="Path to CSV file")
    parser.add_argument("--images", default="data/image", help="Path to images directory")
    parser.add_argument("--sample-size", type=int, default=100, 
                       help="Sample size for image analysis")
    
    args = parser.parse_args()
    
    analyzer = DatasetAnalyzer(args.csv, args.images)
    analyzer.run_full_analysis()

if __name__ == "__main__":
    main()
