#!/usr/bin/env python3
"""
Create training dataset from uploaded damaged car images
"""
import os
import shutil
import cv2
import numpy as np
from pathlib import Path
import argparse
import logging
from typing import List, Tuple, Dict
import json
from datetime import datetime
import random

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatasetCreator:
    """Create dataset from uploaded damaged car images"""
    
    def __init__(self, source_dir: str = "uploads/damaged_cars", output_dir: str = "data/training_dataset"):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Damage type mapping for manual annotation guidance
        self.damage_types = {
            0: "scratch_surface",
            1: "scratch_deep", 
            2: "dent_minor",
            3: "dent_major",
            4: "broken_bumper",
            5: "broken_headlight",
            6: "broken_mirror",
            7: "paint_damage",
            8: "glass_damage",
            9: "structural_damage"
        }
        
    def setup_directories(self):
        """Create dataset directory structure"""
        directories = [
            self.output_dir / "raw_images",
            self.output_dir / "processed_images", 
            self.output_dir / "annotations",
            self.output_dir / "training" / "images",
            self.output_dir / "training" / "labels",
            self.output_dir / "validation" / "images", 
            self.output_dir / "validation" / "labels",
            self.output_dir / "test" / "images",
            self.output_dir / "test" / "labels"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
        logger.info(f"Created directory structure in {self.output_dir}")
        
    def validate_and_process_images(self) -> List[Path]:
        """Validate and process images from uploads"""
        if not self.source_dir.exists():
            raise FileNotFoundError(f"Source directory not found: {self.source_dir}")
            
        # Find all image files
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.JPG', '.JPEG', '.PNG', '.BMP']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(self.source_dir.glob(f"*{ext}"))
            
        logger.info(f"Found {len(image_files)} image files")
        
        # Process and validate images
        valid_images = []
        processed_count = 0
        
        for img_path in image_files:
            try:
                # Load and validate image
                img = cv2.imread(str(img_path))
                if img is None:
                    logger.warning(f"Cannot read image: {img_path}")
                    continue
                    
                height, width = img.shape[:2]
                
                # Skip very small images
                if width < 300 or height < 300:
                    logger.warning(f"Image too small ({width}x{height}): {img_path}")
                    continue
                    
                # Resize if too large (keep aspect ratio)
                max_size = 1920
                if width > max_size or height > max_size:
                    scale = max_size / max(width, height)
                    new_width = int(width * scale)
                    new_height = int(height * scale)
                    img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)
                    
                # Generate standardized filename
                new_filename = f"damage_{processed_count:04d}.jpg"
                processed_path = self.output_dir / "processed_images" / new_filename
                
                # Save processed image
                cv2.imwrite(str(processed_path), img, [cv2.IMWRITE_JPEG_QUALITY, 90])
                
                # Copy original to raw_images
                raw_path = self.output_dir / "raw_images" / f"original_{processed_count:04d}{img_path.suffix}"
                shutil.copy2(img_path, raw_path)
                
                valid_images.append({
                    'processed_path': processed_path,
                    'raw_path': raw_path,
                    'original_path': img_path,
                    'width': img.shape[1],
                    'height': img.shape[0]
                })
                
                processed_count += 1
                
            except Exception as e:
                logger.error(f"Error processing {img_path}: {e}")
                continue
                
        logger.info(f"Successfully processed {len(valid_images)} images")
        return valid_images
        
    def create_annotation_template(self, valid_images: List[Dict]):
        """Create annotation template and instructions"""
        
        # Create annotation instructions
        instructions = f"""
# Vehicle Damage Annotation Instructions

## Dataset Information
- Total Images: {len(valid_images)}
- Created: {self.timestamp}
- Source: {self.source_dir}

## Damage Classes (YOLO Format)
"""
        for class_id, damage_type in self.damage_types.items():
            instructions += f"{class_id}: {damage_type}\n"
            
        instructions += """
## Annotation Process

### Step 1: Install LabelImg
```bash
pip install labelImg
```

### Step 2: Start Annotation
```bash
labelImg data/training_dataset/processed_images data/training_dataset/annotations
```

### Step 3: Annotation Guidelines
1. **Bounding Box**: Draw tight boxes around each damage area
2. **Class Selection**: Choose appropriate damage type from dropdown
3. **Multiple Damages**: Create separate boxes for each damage in same image
4. **Quality**: Only annotate clearly visible damages
5. **Consistency**: Use same criteria throughout

### Step 4: Save Format
- Format: YOLO
- Files saved to: data/training_dataset/annotations/
- Each image gets corresponding .txt file

## Damage Type Guidelines

### 0: scratch_surface
- Light surface scratches
- Can be polished out
- No primer/metal visible

### 1: scratch_deep  
- Deep scratches reaching primer/metal
- Cannot be polished out
- Requires touch-up paint

### 2: dent_minor
- Small dents < 5cm diameter
- No paint damage
- Can be pulled out

### 3: dent_major
- Large dents > 5cm diameter
- May have paint damage
- Requires body work

### 4: broken_bumper
- Cracked, broken, or detached bumper
- Missing bumper pieces
- Significant structural damage

### 5: broken_headlight
- Damaged headlight assembly
- Cracked lens or housing
- Missing headlight

### 6: broken_mirror
- Damaged side/rear view mirror
- Cracked mirror glass
- Missing mirror assembly

### 7: paint_damage
- Paint chips, fading, discoloration
- Rust spots
- Clear coat damage

### 8: glass_damage
- Cracked or broken windows
- Windshield damage
- Side window damage

### 9: structural_damage
- Frame or structural component damage
- Severe body damage
- Safety-critical damage

## Quality Control
- Annotate ALL visible damages in each image
- Review annotations before saving
- Maintain consistent labeling standards
- Skip unclear or ambiguous damages

## Next Steps After Annotation
1. Run: `python scripts/prepare_annotated_dataset.py`
2. Train model: `python scripts/train_small_dataset.py --data data/training_dataset`
"""
        
        instructions_path = self.output_dir / "annotation_instructions.md"
        with open(instructions_path, 'w') as f:
            f.write(instructions)
            
        logger.info(f"Created annotation instructions: {instructions_path}")
        
        # Create image list for reference
        image_list = []
        for i, img_info in enumerate(valid_images):
            image_list.append({
                'id': i,
                'filename': img_info['processed_path'].name,
                'original': str(img_info['original_path']),
                'width': img_info['width'],
                'height': img_info['height']
            })
            
        list_path = self.output_dir / "image_list.json"
        with open(list_path, 'w') as f:
            json.dump(image_list, f, indent=2)
            
        logger.info(f"Created image list: {list_path}")
        
    def create_sample_annotations(self, valid_images: List[Dict], num_samples: int = 5):
        """Create sample annotation files to demonstrate format"""
        sample_dir = self.output_dir / "sample_annotations"
        sample_dir.mkdir(exist_ok=True)
        
        # Create sample annotations for first few images
        for i in range(min(num_samples, len(valid_images))):
            img_info = valid_images[i]
            img_name = img_info['processed_path'].stem
            
            # Create sample annotation (you would replace this with actual annotations)
            sample_annotation = f"""# Sample annotation for {img_name}.jpg
# Format: class_id center_x center_y width height (all normalized 0-1)
# 
# Example annotations (REPLACE WITH ACTUAL DAMAGE LOCATIONS):
# 0 0.5 0.3 0.2 0.1    # scratch_surface at center-top
# 2 0.7 0.6 0.15 0.12  # dent_minor at right side
# 7 0.3 0.8 0.25 0.08  # paint_damage at bottom-left
#
# INSTRUCTIONS:
# 1. Remove all # comments
# 2. Add actual damage annotations
# 3. Save as {img_name}.txt in annotations/ folder
"""
            
            sample_path = sample_dir / f"{img_name}_sample.txt"
            with open(sample_path, 'w') as f:
                f.write(sample_annotation)
                
        logger.info(f"Created {num_samples} sample annotation files in {sample_dir}")
        
    def generate_dataset_report(self, valid_images: List[Dict]):
        """Generate comprehensive dataset report"""
        report = f"""
# Vehicle Damage Dataset Report

## Dataset Summary
- **Creation Date**: {self.timestamp}
- **Source Directory**: {self.source_dir}
- **Output Directory**: {self.output_dir}
- **Total Valid Images**: {len(valid_images)}
- **Original Images Found**: {len(list(self.source_dir.glob("*")))}

## Image Statistics
- **Average Resolution**: {np.mean([img['width'] for img in valid_images]):.0f} x {np.mean([img['height'] for img in valid_images]):.0f}
- **Min Resolution**: {min([img['width'] for img in valid_images])} x {min([img['height'] for img in valid_images])}
- **Max Resolution**: {max([img['width'] for img in valid_images])} x {max([img['height'] for img in valid_images])}

## Dataset Structure
```
{self.output_dir}/
├── raw_images/           # Original uploaded images
├── processed_images/     # Standardized images for annotation
├── annotations/          # YOLO annotation files (to be created)
├── sample_annotations/   # Example annotation files
├── training/            # Training split (80%)
├── validation/          # Validation split (15%)
├── test/               # Test split (5%)
└── reports/            # Dataset reports and statistics
```

## Annotation Workflow
1. **Install LabelImg**: `pip install labelImg`
2. **Start Annotation**: `labelImg {self.output_dir}/processed_images {self.output_dir}/annotations`
3. **Follow Guidelines**: See annotation_instructions.md
4. **Quality Control**: Review annotations regularly
5. **Prepare Dataset**: Run prepare_annotated_dataset.py

## Expected Training Performance
With {len(valid_images)} images and proper annotation:
- **Expected mAP@0.5**: 0.5-0.6 (50-60%)
- **Training Time**: 2-4 hours on GPU
- **Annotation Time**: 2-3 weeks (2-3 minutes per image)
- **Total Project Time**: 4-5 weeks

## Damage Type Distribution (Target)
Based on typical vehicle damage patterns:
- **Scratches (0,1)**: ~30% ({int(len(valid_images) * 0.3)} images)
- **Dents (2,3)**: ~25% ({int(len(valid_images) * 0.25)} images)  
- **Paint Damage (7)**: ~20% ({int(len(valid_images) * 0.2)} images)
- **Broken Parts (4,5,6)**: ~15% ({int(len(valid_images) * 0.15)} images)
- **Glass/Structural (8,9)**: ~10% ({int(len(valid_images) * 0.1)} images)

## Next Steps
1. **Annotate Images**: Use LabelImg to create YOLO annotations
2. **Quality Review**: Check 20% of annotations for consistency
3. **Prepare Dataset**: Split into train/val/test sets
4. **Train Model**: Use optimized small dataset training
5. **Evaluate Results**: Test on real vehicle images

## Files Created
- `processed_images/`: {len(valid_images)} standardized images
- `raw_images/`: {len(valid_images)} original images  
- `annotation_instructions.md`: Detailed annotation guide
- `image_list.json`: Image metadata and references
- `sample_annotations/`: Example annotation files
- `dataset_report.md`: This report

## Contact & Support
For annotation questions or technical issues, refer to:
- annotation_instructions.md
- YOLO annotation format documentation
- LabelImg user guide
"""
        
        report_path = self.output_dir / "dataset_report.md"
        with open(report_path, 'w') as f:
            f.write(report)
            
        logger.info(f"Generated dataset report: {report_path}")
        
    def create_dataset(self):
        """Main function to create dataset from uploads"""
        logger.info("🚀 Starting dataset creation from uploaded images...")
        
        try:
            # Step 1: Setup directories
            self.setup_directories()
            
            # Step 2: Process and validate images
            valid_images = self.validate_and_process_images()
            
            if len(valid_images) < 100:
                logger.warning(f"Only {len(valid_images)} valid images found. Consider adding more images.")
            
            # Step 3: Create annotation template and instructions
            self.create_annotation_template(valid_images)
            
            # Step 4: Create sample annotations
            self.create_sample_annotations(valid_images)
            
            # Step 5: Generate comprehensive report
            self.generate_dataset_report(valid_images)
            
            logger.info("✅ Dataset creation completed successfully!")
            logger.info(f"📊 Processed {len(valid_images)} images")
            logger.info(f"📁 Dataset location: {self.output_dir}")
            logger.info(f"📋 Next step: Annotate images using LabelImg")
            logger.info(f"📖 Read: {self.output_dir}/annotation_instructions.md")
            
            return valid_images
            
        except Exception as e:
            logger.error(f"❌ Dataset creation failed: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description="Create training dataset from uploaded images")
    parser.add_argument("--source", default="uploads/damaged_cars", 
                       help="Source directory with uploaded images")
    parser.add_argument("--output", default="data/training_dataset",
                       help="Output directory for dataset")
    
    args = parser.parse_args()
    
    # Create dataset
    creator = DatasetCreator(args.source, args.output)
    valid_images = creator.create_dataset()
    
    print(f"\n🎉 Dataset Creation Summary:")
    print(f"   📸 Images processed: {len(valid_images)}")
    print(f"   📁 Dataset location: {args.output}")
    print(f"   📋 Next steps:")
    print(f"      1. Read annotation instructions: {args.output}/annotation_instructions.md")
    print(f"      2. Install LabelImg: pip install labelImg")
    print(f"      3. Start annotating: labelImg {args.output}/processed_images {args.output}/annotations")
    print(f"      4. After annotation: python scripts/prepare_annotated_dataset.py")

if __name__ == "__main__":
    main()
