#!/usr/bin/env python3
"""
Annotation helper to speed up manual annotation process
"""
import cv2
import numpy as np
from pathlib import Path
import argparse
import logging
import json
from typing import List, Dict, Tuple

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AnnotationHelper:
    """Helper to analyze images and suggest annotation strategies"""
    
    def __init__(self, images_dir: str):
        self.images_dir = Path(images_dir)
        
    def analyze_image_characteristics(self, image_path: Path) -> Dict:
        """Analyze image to suggest likely damage types"""
        image = cv2.imread(str(image_path))
        if image is None:
            return {}
            
        height, width = image.shape[:2]
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        analysis = {
            'filename': image_path.name,
            'resolution': f"{width}x{height}",
            'likely_damages': [],
            'annotation_priority': 'medium',
            'suggestions': []
        }
        
        # Analyze brightness and contrast
        mean_brightness = np.mean(gray)
        contrast = np.std(gray)
        
        if mean_brightness < 80:
            analysis['suggestions'].append("Dark image - look for shadow-based dents")
            analysis['likely_damages'].append("dent_minor")
            analysis['likely_damages'].append("dent_major")
        
        if contrast > 60:
            analysis['suggestions'].append("High contrast - good for detecting scratches")
            analysis['likely_damages'].append("scratch_surface")
            analysis['likely_damages'].append("scratch_deep")
        
        # Edge detection analysis
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / (width * height)
        
        if edge_density > 0.1:
            analysis['annotation_priority'] = 'high'
            analysis['suggestions'].append("Many edges detected - likely structural damage")
            analysis['likely_damages'].append("structural_damage")
            analysis['likely_damages'].append("broken_bumper")
        
        # Color analysis
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        color_variance = np.var(hsv[:,:,1])  # Saturation variance
        
        if color_variance > 1000:
            analysis['suggestions'].append("Color inconsistencies - check for paint damage")
            analysis['likely_damages'].append("paint_damage")
        
        # Detect potential glass areas (high brightness, low saturation)
        bright_mask = gray > 200
        if np.sum(bright_mask) > (width * height * 0.1):
            analysis['suggestions'].append("Bright areas detected - check for glass damage")
            analysis['likely_damages'].append("glass_damage")
        
        return analysis
    
    def create_annotation_guide(self, analyses: List[Dict]):
        """Create a comprehensive annotation guide"""
        
        # Categorize images by priority
        high_priority = [a for a in analyses if a.get('annotation_priority') == 'high']
        medium_priority = [a for a in analyses if a.get('annotation_priority') == 'medium']
        low_priority = [a for a in analyses if a.get('annotation_priority') == 'low']
        
        # Count damage type suggestions
        damage_counts = {}
        for analysis in analyses:
            for damage in analysis.get('likely_damages', []):
                damage_counts[damage] = damage_counts.get(damage, 0) + 1
        
        guide_content = f"""# 🎯 Smart Annotation Guide for {len(analyses)} Images

## 📊 Dataset Analysis Summary

### Priority Distribution
- **High Priority**: {len(high_priority)} images (complex damage, annotate first)
- **Medium Priority**: {len(medium_priority)} images (standard annotation)
- **Low Priority**: {len(low_priority)} images (simple or unclear damage)

### Predicted Damage Distribution
"""
        
        damage_types = {
            'scratch_surface': 'Surface Scratches',
            'scratch_deep': 'Deep Scratches', 
            'dent_minor': 'Minor Dents',
            'dent_major': 'Major Dents',
            'broken_bumper': 'Broken Bumpers',
            'broken_headlight': 'Broken Headlights',
            'broken_mirror': 'Broken Mirrors',
            'paint_damage': 'Paint Damage',
            'glass_damage': 'Glass Damage',
            'structural_damage': 'Structural Damage'
        }
        
        for damage_type, count in sorted(damage_counts.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / len(analyses)) * 100
            guide_content += f"- **{damage_types.get(damage_type, damage_type)}**: {count} images ({percentage:.1f}%)\n"
        
        guide_content += f"""
## 🚀 Recommended Annotation Strategy

### Phase 1: High Priority Images ({len(high_priority)} images)
Start with these images as they likely contain clear, multiple damages:
"""
        
        for analysis in high_priority[:10]:  # Show first 10
            guide_content += f"- **{analysis['filename']}**: {', '.join(analysis.get('suggestions', []))}\n"
        
        if len(high_priority) > 10:
            guide_content += f"... and {len(high_priority) - 10} more high-priority images\n"
        
        guide_content += f"""
### Phase 2: Medium Priority Images ({len(medium_priority)} images)
Standard annotation process for these images.

### Phase 3: Low Priority Images ({len(low_priority)} images)
Review these last - may have unclear or no damage.

## 💡 Annotation Tips by Damage Type

### Most Common Damages in Your Dataset:
"""
        
        top_damages = sorted(damage_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        tips = {
            'scratch_surface': "Look for light lines on paint surface. Draw tight boxes around scratch lines.",
            'scratch_deep': "Look for scratches exposing primer/metal. Usually darker or different colored lines.",
            'dent_minor': "Small circular/oval depressions. Look for shadow patterns and highlight reflections.",
            'dent_major': "Large depressions affecting significant surface area. Often with paint damage.",
            'paint_damage': "Color inconsistencies, chips, fading. Box the entire affected paint area.",
            'glass_damage': "Cracks, chips, or shattered areas in windows/windshield.",
            'broken_bumper': "Cracks, missing pieces, or detached bumper sections.",
            'structural_damage': "Frame damage, significant body deformation. Usually large affected areas."
        }
        
        for damage_type, count in top_damages:
            if damage_type in tips:
                guide_content += f"\n**{damage_types.get(damage_type, damage_type)}** ({count} images):\n"
                guide_content += f"{tips[damage_type]}\n"
        
        guide_content += """
## ⏱️ Time Estimation

Based on your dataset analysis:
- **High Priority Images**: 3-4 minutes each (complex damage)
- **Medium Priority Images**: 2-3 minutes each (standard)
- **Low Priority Images**: 1-2 minutes each (simple/none)

**Total Estimated Time**: {total_time:.1f} hours ({days:.1f} days at 4 hours/day)

## 🎯 Quality Control Checklist

For each image, ensure you:
- [ ] Annotated ALL visible damages
- [ ] Used tight bounding boxes
- [ ] Chose correct damage class
- [ ] Avoided overlapping boxes for same damage
- [ ] Skipped unclear/ambiguous areas

## 📋 Annotation Order Recommendation

1. Start with: `{first_files}`
2. These images likely have clear, multiple damages
3. Use them to establish your annotation standards
4. Then proceed through remaining images systematically

Good luck with your annotation! 🚀
""".format(
            total_time=(len(high_priority) * 3.5 + len(medium_priority) * 2.5 + len(low_priority) * 1.5) / 60,
            days=(len(high_priority) * 3.5 + len(medium_priority) * 2.5 + len(low_priority) * 1.5) / 60 / 4,
            first_files="`, `".join([a['filename'] for a in high_priority[:5]])
        )
        
        return guide_content
    
    def analyze_dataset(self):
        """Analyze entire dataset and create annotation guide"""
        logger.info("🔍 Analyzing dataset for annotation guidance...")
        
        if not self.images_dir.exists():
            raise FileNotFoundError(f"Images directory not found: {self.images_dir}")
        
        image_files = list(self.images_dir.glob("*.jpg"))
        logger.info(f"Found {len(image_files)} images to analyze")
        
        analyses = []
        for i, image_path in enumerate(image_files):
            if i % 50 == 0:
                logger.info(f"Analyzing {i+1}/{len(image_files)} images...")
            
            analysis = self.analyze_image_characteristics(image_path)
            analyses.append(analysis)
        
        # Create annotation guide
        guide_content = self.create_annotation_guide(analyses)
        
        # Save guide
        guide_file = self.images_dir.parent / "smart_annotation_guide.md"
        with open(guide_file, 'w') as f:
            f.write(guide_content)
        
        # Save detailed analysis as JSON
        json_file = self.images_dir.parent / "image_analysis.json"
        with open(json_file, 'w') as f:
            json.dump(analyses, f, indent=2)
        
        logger.info("✅ Dataset analysis completed!")
        logger.info(f"📄 Annotation guide: {guide_file}")
        logger.info(f"📊 Detailed analysis: {json_file}")
        
        # Print summary
        high_priority = len([a for a in analyses if a.get('annotation_priority') == 'high'])
        logger.info(f"📋 Summary:")
        logger.info(f"   - High priority images: {high_priority}")
        logger.info(f"   - Estimated annotation time: {(len(analyses) * 2.5) / 60:.1f} hours")
        
        return analyses

def main():
    parser = argparse.ArgumentParser(description="Analyze dataset for smart annotation guidance")
    parser.add_argument("--images", default="data/training_dataset/processed_images",
                       help="Directory containing images to analyze")
    
    args = parser.parse_args()
    
    helper = AnnotationHelper(args.images)
    helper.analyze_dataset()

if __name__ == "__main__":
    main()
