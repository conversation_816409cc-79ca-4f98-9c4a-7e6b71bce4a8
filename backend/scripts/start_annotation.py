#!/usr/bin/env python3
"""
Helper script to start the annotation process
"""
import subprocess
import sys
import os
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_labelimg_installed():
    """Check if labelImg is installed"""
    try:
        result = subprocess.run(['labelImg', '--help'], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def install_labelimg():
    """Install labelImg"""
    logger.info("Installing labelImg...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'labelImg'], check=True)
        logger.info("✅ labelImg installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install labelImg: {e}")
        return False

def create_predefined_classes():
    """Create predefined_classes.txt for labelImg"""
    classes = [
        "scratch_surface",
        "scratch_deep", 
        "dent_minor",
        "dent_major",
        "broken_bumper",
        "broken_headlight",
        "broken_mirror",
        "paint_damage",
        "glass_damage",
        "structural_damage"
    ]
    
    classes_file = Path("data/training_dataset/predefined_classes.txt")
    with open(classes_file, 'w') as f:
        for class_name in classes:
            f.write(f"{class_name}\n")
    
    logger.info(f"Created predefined classes file: {classes_file}")
    return classes_file

def start_annotation():
    """Start the annotation process"""
    logger.info("🚀 Starting Vehicle Damage Annotation Process")
    
    # Check if dataset exists
    dataset_dir = Path("data/training_dataset")
    if not dataset_dir.exists():
        logger.error("❌ Dataset not found. Run create_dataset_from_uploads.py first.")
        return False
    
    images_dir = dataset_dir / "processed_images"
    annotations_dir = dataset_dir / "annotations"
    
    if not images_dir.exists():
        logger.error("❌ Processed images directory not found.")
        return False
    
    # Count images
    image_count = len(list(images_dir.glob("*.jpg")))
    logger.info(f"📸 Found {image_count} images to annotate")
    
    # Check if labelImg is installed
    if not check_labelimg_installed():
        logger.info("📦 labelImg not found. Installing...")
        if not install_labelimg():
            return False
    
    # Create predefined classes file
    classes_file = create_predefined_classes()
    
    # Display annotation instructions
    print("\n" + "="*60)
    print("🎯 VEHICLE DAMAGE ANNOTATION INSTRUCTIONS")
    print("="*60)
    print(f"📁 Images to annotate: {image_count}")
    print(f"📂 Images location: {images_dir}")
    print(f"💾 Annotations will be saved to: {annotations_dir}")
    print("\n📋 Damage Classes:")
    print("  0: scratch_surface   - Light surface scratches")
    print("  1: scratch_deep      - Deep scratches to primer/metal")
    print("  2: dent_minor        - Small dents < 5cm")
    print("  3: dent_major        - Large dents > 5cm")
    print("  4: broken_bumper     - Cracked/broken bumper")
    print("  5: broken_headlight  - Damaged headlight")
    print("  6: broken_mirror     - Damaged mirror")
    print("  7: paint_damage      - Paint chips/fading")
    print("  8: glass_damage      - Cracked windows")
    print("  9: structural_damage - Frame/structural damage")
    
    print("\n🎯 Annotation Tips:")
    print("  • Draw tight bounding boxes around damage areas")
    print("  • Annotate ALL visible damages in each image")
    print("  • Use consistent criteria throughout")
    print("  • Skip unclear or ambiguous damages")
    print("  • Save in YOLO format")
    
    print("\n⏱️  Estimated Time:")
    print(f"  • At 2-3 minutes per image: {image_count * 2.5 / 60:.1f} hours")
    print(f"  • Working 4 hours/day: {image_count * 2.5 / 60 / 4:.1f} days")
    
    print("\n🚀 Starting labelImg...")
    print("="*60)
    
    # Start labelImg
    try:
        cmd = [
            'labelImg',
            str(images_dir),
            str(classes_file),
            str(annotations_dir)
        ]
        
        logger.info(f"Running: {' '.join(cmd)}")
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        logger.info("\n⏹️  Annotation stopped by user")
    except Exception as e:
        logger.error(f"❌ Error starting labelImg: {e}")
        logger.info("💡 Try running manually:")
        logger.info(f"   labelImg {images_dir} {classes_file} {annotations_dir}")
        return False
    
    # Check annotation progress
    annotation_count = len(list(annotations_dir.glob("*.txt")))
    logger.info(f"\n📊 Annotation Progress: {annotation_count}/{image_count} images annotated")
    
    if annotation_count > 0:
        logger.info("✅ Great! You've started annotating.")
        logger.info("📋 Next steps after completing annotations:")
        logger.info("   1. python scripts/prepare_annotated_dataset.py")
        logger.info("   2. python scripts/train_small_dataset.py --data data/training_dataset")
    
    return True

def main():
    """Main function"""
    if not start_annotation():
        sys.exit(1)

if __name__ == "__main__":
    main()
