#!/usr/bin/env python3
"""
Data augmentation script for vehicle damage detection dataset
"""
import cv2
import numpy as np
import albumentations as A
from pathlib import Path
import argparse
import logging
from typing import List, Tuple
import random
import json

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataAugmentor:
    """Data augmentation for vehicle damage images"""
    
    def __init__(self, input_dir: str, output_dir: str, augment_factor: int = 3):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.augment_factor = augment_factor
        
        # Define augmentation pipeline
        self.transform = A.Compose([
            # Geometric transformations
            A.HorizontalFlip(p=0.5),
            <PERSON>.<PERSON>ota<PERSON>(p=0.3),
            <PERSON><PERSON>(limit=15, p=0.5),
            <PERSON>.<PERSON>ft<PERSON>cale<PERSON>otate(
                shift_limit=0.1, 
                scale_limit=0.2, 
                rotate_limit=15, 
                p=0.5
            ),
            
            # Color and lighting augmentations
            A.RandomBrightnessContrast(
                brightness_limit=0.2, 
                contrast_limit=0.2, 
                p=0.5
            ),
            <PERSON><PERSON>Value(
                hue_shift_limit=10, 
                sat_shift_limit=20, 
                val_shift_limit=20, 
                p=0.5
            ),
            A.RGBShift(r_shift_limit=15, g_shift_limit=15, b_shift_limit=15, p=0.3),
            
            # Weather and environmental effects
            A.RandomSunFlare(
                flare_roi=(0, 0, 1, 0.5), 
                angle_lower=0, 
                angle_upper=1, 
                num_flare_circles_lower=1, 
                num_flare_circles_upper=2, 
                p=0.1
            ),
            A.RandomRain(
                slant_lower=-10, 
                slant_upper=10, 
                drop_length=20, 
                drop_width=1, 
                drop_color=(200, 200, 200), 
                blur_value=7, 
                brightness_coefficient=0.7, 
                rain_type=None, 
                p=0.1
            ),
            A.RandomFog(fog_coef_lower=0.1, fog_coef_upper=0.3, alpha_coef=0.08, p=0.1),
            
            # Noise and blur
            A.GaussNoise(var_limit=(10.0, 50.0), p=0.2),
            A.MotionBlur(blur_limit=3, p=0.2),
            A.GaussianBlur(blur_limit=3, p=0.1),
            
            # JPEG compression simulation
            A.ImageCompression(quality_lower=60, quality_upper=100, p=0.3),
            
        ], bbox_params=A.BboxParams(
            format='yolo',
            label_fields=['class_labels'],
            min_visibility=0.3  # Keep bboxes with at least 30% visibility
        ))
        
    def load_annotations(self, label_path: Path) -> Tuple[List[List[float]], List[int]]:
        """Load YOLO format annotations"""
        bboxes = []
        class_labels = []
        
        if label_path.exists():
            with open(label_path, 'r') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) == 5:
                        class_id = int(parts[0])
                        bbox = [float(x) for x in parts[1:]]  # [x_center, y_center, width, height]
                        bboxes.append(bbox)
                        class_labels.append(class_id)
                        
        return bboxes, class_labels
        
    def save_annotations(self, bboxes: List[List[float]], class_labels: List[int], 
                        output_path: Path):
        """Save YOLO format annotations"""
        with open(output_path, 'w') as f:
            for bbox, class_id in zip(bboxes, class_labels):
                f.write(f"{class_id} {' '.join(map(str, bbox))}\n")
                
    def augment_image(self, image_path: Path, label_path: Path, 
                     output_prefix: str) -> int:
        """Augment a single image and its annotations"""
        # Load image
        image = cv2.imread(str(image_path))
        if image is None:
            logger.warning(f"Cannot load image: {image_path}")
            return 0
            
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Load annotations
        bboxes, class_labels = self.load_annotations(label_path)
        
        augmented_count = 0
        
        for i in range(self.augment_factor):
            try:
                # Apply augmentation
                if bboxes:  # If there are annotations
                    transformed = self.transform(
                        image=image, 
                        bboxes=bboxes, 
                        class_labels=class_labels
                    )
                    aug_image = transformed['image']
                    aug_bboxes = transformed['bboxes']
                    aug_class_labels = transformed['class_labels']
                else:  # No annotations (background images)
                    transformed = self.transform(image=image)
                    aug_image = transformed['image']
                    aug_bboxes = []
                    aug_class_labels = []
                
                # Save augmented image
                aug_image_path = self.output_dir / 'images' / f"{output_prefix}_aug_{i}{image_path.suffix}"
                aug_image_bgr = cv2.cvtColor(aug_image, cv2.COLOR_RGB2BGR)
                cv2.imwrite(str(aug_image_path), aug_image_bgr)
                
                # Save augmented annotations
                aug_label_path = self.output_dir / 'labels' / f"{output_prefix}_aug_{i}.txt"
                self.save_annotations(aug_bboxes, aug_class_labels, aug_label_path)
                
                augmented_count += 1
                
            except Exception as e:
                logger.warning(f"Failed to augment {image_path} (attempt {i}): {e}")
                continue
                
        return augmented_count
        
    def augment_dataset(self):
        """Augment entire dataset"""
        # Create output directories
        (self.output_dir / 'images').mkdir(parents=True, exist_ok=True)
        (self.output_dir / 'labels').mkdir(parents=True, exist_ok=True)
        
        # Find all images
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        image_paths = []
        
        for ext in image_extensions:
            image_paths.extend(self.input_dir.glob(f"**/*{ext}"))
            image_paths.extend(self.input_dir.glob(f"**/*{ext.upper()}"))
            
        logger.info(f"Found {len(image_paths)} images to augment")
        
        total_augmented = 0
        successful_images = 0
        
        for img_path in image_paths:
            # Find corresponding label file
            label_path = img_path.with_suffix('.txt')
            
            # Generate output prefix
            output_prefix = f"{img_path.parent.name}_{img_path.stem}"
            
            # Augment image
            count = self.augment_image(img_path, label_path, output_prefix)
            
            if count > 0:
                successful_images += 1
                total_augmented += count
                
        logger.info(f"Augmentation completed:")
        logger.info(f"  - Processed: {successful_images}/{len(image_paths)} images")
        logger.info(f"  - Generated: {total_augmented} augmented images")
        
        # Save augmentation report
        report = {
            'input_directory': str(self.input_dir),
            'output_directory': str(self.output_dir),
            'augmentation_factor': self.augment_factor,
            'original_images': len(image_paths),
            'successfully_processed': successful_images,
            'total_augmented_images': total_augmented,
            'augmentation_pipeline': [
                'HorizontalFlip', 'RandomRotate90', 'Rotate', 'ShiftScaleRotate',
                'RandomBrightnessContrast', 'HueSaturationValue', 'RGBShift',
                'RandomSunFlare', 'RandomRain', 'RandomFog',
                'GaussNoise', 'MotionBlur', 'GaussianBlur', 'ImageCompression'
            ]
        }
        
        report_path = self.output_dir / 'augmentation_report.json'
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
            
        logger.info(f"Augmentation report saved to: {report_path}")

def main():
    parser = argparse.ArgumentParser(description="Augment vehicle damage dataset")
    parser.add_argument("--input", required=True, help="Input directory with images and labels")
    parser.add_argument("--output", required=True, help="Output directory for augmented data")
    parser.add_argument("--factor", type=int, default=3, 
                       help="Augmentation factor (number of augmented versions per image)")
    
    args = parser.parse_args()
    
    # Initialize augmentor
    augmentor = DataAugmentor(args.input, args.output, args.factor)
    
    # Start augmentation
    augmentor.augment_dataset()

if __name__ == "__main__":
    main()
