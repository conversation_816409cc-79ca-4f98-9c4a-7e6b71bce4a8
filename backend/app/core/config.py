"""
Configuration settings for the application
"""
from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    # Application
    APP_NAME: str = "Car Damage Detection API"
    DEBUG: bool = True
    VERSION: str = "1.0.0"

    # Database
    DATABASE_URL: str = "sqlite:///./car_damage.db"

    # Security
    SECRET_KEY: str = "your-secret-key-here-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # CORS
    ALLOWED_HOSTS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]

    # AI Models
    DAMAGE_DETECTION_MODEL_PATH: str = "models/damage_detection.pt"
    COST_ESTIMATION_MODEL_PATH: str = "models/cost_estimation.pkl"

    # File Upload
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS: List[str] = [".jpg", ".jpeg", ".png", ".bmp"]
    UPLOAD_DIR: str = "uploads"

    # Tunisian Market Data
    CURRENCY: str = "TND"
    TAX_RATE: float = 0.19  # 19% VAT in Tunisia
    LABOR_RATE_PER_HOUR: float = 25.0  # TND per hour

    # Damage Categories
    DAMAGE_TYPES: List[str] = [
        "scratch_surface",
        "scratch_deep",
        "dent_minor",
        "dent_major",
        "broken_bumper",
        "broken_headlight",
        "broken_mirror",
        "paint_damage",
        "glass_damage",
        "structural_damage"
    ]

    SEVERITY_LEVELS: List[str] = ["minor", "moderate", "severe"]

    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
