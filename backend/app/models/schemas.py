"""
Pydantic models for API requests and responses
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class SeverityLevel(str, Enum):
    MINOR = "minor"
    MODERATE = "moderate"
    SEVERE = "severe"

class DamageType(str, Enum):
    SCRATCH_SURFACE = "scratch_surface"
    SCRATCH_DEEP = "scratch_deep"
    DENT_MINOR = "dent_minor"
    DENT_MAJOR = "dent_major"
    BROKEN_BUMPER = "broken_bumper"
    BROKEN_HEADLIGHT = "broken_headlight"
    BROKEN_MIRROR = "broken_mirror"
    PAINT_DAMAGE = "paint_damage"
    GLASS_DAMAGE = "glass_damage"
    STRUCTURAL_DAMAGE = "structural_damage"

class DetectedDamage(BaseModel):
    """Schema for individual damage detection"""
    damage_type: DamageType
    severity: SeverityLevel
    confidence: float = Field(..., ge=0.0, le=1.0)
    bounding_box: List[float] = Field(..., min_items=4, max_items=4)  # [x1, y1, x2, y2]
    affected_area: float = Field(..., ge=0.0)  # Percentage of affected area
    description: Optional[str] = None

class DamageAnalysisRequest(BaseModel):
    """Request schema for damage analysis"""
    vehicle_make: Optional[str] = None
    vehicle_model: Optional[str] = None
    vehicle_year: Optional[int] = None
    additional_info: Optional[Dict[str, Any]] = None

class DamageAnalysisResponse(BaseModel):
    """Response schema for damage analysis"""
    analysis_id: int
    detected_damages: List[DetectedDamage]
    overall_severity: SeverityLevel
    total_estimated_cost: float
    confidence_score: float
    processing_time: float
    created_at: datetime
    
    class Config:
        from_attributes = True

class CostBreakdownItem(BaseModel):
    """Schema for individual cost breakdown item"""
    item_name: str
    item_type: str  # parts, labor, paint, tax
    quantity: float = 1.0
    unit_cost: float
    total_cost: float
    description: Optional[str] = None

class CostBreakdownResponse(BaseModel):
    """Response schema for cost breakdown"""
    analysis_id: int
    parts_cost: float
    labor_cost: float
    paint_cost: float
    tax_amount: float
    total_cost: float
    cost_details: List[CostBreakdownItem]
    currency: str = "TND"
    
    class Config:
        from_attributes = True

class VehicleInfo(BaseModel):
    """Schema for vehicle information"""
    make: str
    model: str
    year: int
    vin: Optional[str] = None
    color: Optional[str] = None
    mileage: Optional[int] = None

class IncidentDetails(BaseModel):
    """Schema for incident details"""
    date: datetime
    location: Optional[str] = None
    description: Optional[str] = None
    weather_conditions: Optional[str] = None
    driver_info: Optional[Dict[str, Any]] = None

class InsuranceReportRequest(BaseModel):
    """Request schema for insurance report generation"""
    analysis_id: int
    vehicle_info: VehicleInfo
    incident_details: IncidentDetails
    additional_notes: Optional[str] = None

class RepairRecommendation(BaseModel):
    """Schema for repair recommendations"""
    damage_type: DamageType
    recommended_action: str  # repair, replace, ignore
    priority: str  # high, medium, low
    estimated_time: int  # hours
    cost_estimate: float
    notes: Optional[str] = None

class InsuranceReportResponse(BaseModel):
    """Response schema for insurance report"""
    report_id: int
    report_number: str
    analysis_id: int
    vehicle_info: VehicleInfo
    incident_details: IncidentDetails
    damage_summary: str
    repair_recommendations: List[RepairRecommendation]
    total_cost_estimate: float
    estimated_repair_time: int  # days
    status: str
    created_at: datetime
    
    class Config:
        from_attributes = True

class ErrorResponse(BaseModel):
    """Schema for error responses"""
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None

class HealthCheckResponse(BaseModel):
    """Schema for health check response"""
    status: str
    timestamp: datetime
    version: str
