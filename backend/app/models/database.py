"""
Database models for the car damage detection system
"""
from sqlalchemy import <PERSON>umn, Integer, String, Float, DateTime, Text, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class DamageAnalysis(Base):
    """Model for storing damage analysis results"""
    __tablename__ = "damage_analyses"

    id = Column(Integer, primary_key=True, index=True)
    image_path = Column(String, nullable=False)
    original_filename = Column(String, nullable=False)

    # Detection results
    detected_damages = Column(JSON)  # List of detected damage objects
    confidence_scores = Column(JSON)  # Confidence scores for each detection

    # Overall assessment
    overall_severity = Column(String)  # minor, moderate, severe

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())



class InsuranceReport(Base):
    """Model for insurance reports"""
    __tablename__ = "insurance_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("damage_analyses.id"))
    
    # Report details
    report_number = Column(String, unique=True, index=True)
    vehicle_info = Column(JSON)  # Make, model, year, VIN, etc.
    incident_details = Column(JSON)  # Date, location, description
    
    # Assessment
    damage_summary = Column(Text)
    repair_recommendations = Column(JSON)
    estimated_repair_time = Column(Integer)  # Days
    
    # Status
    status = Column(String, default="draft")  # draft, submitted, approved, rejected
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    analysis = relationship("DamageAnalysis")
