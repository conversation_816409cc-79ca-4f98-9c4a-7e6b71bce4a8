"""
Database models for the car damage detection system
"""
from sqlalchemy import <PERSON>umn, Integer, String, Float, DateTime, Text, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base

class DamageAnalysis(Base):
    """Model for storing damage analysis results"""
    __tablename__ = "damage_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    image_path = Column(String, nullable=False)
    original_filename = Column(String, nullable=False)
    
    # Detection results
    detected_damages = Column(JSON)  # List of detected damage objects
    confidence_scores = Column(JSON)  # Confidence scores for each detection
    
    # Overall assessment
    overall_severity = Column(String)  # minor, moderate, severe
    total_estimated_cost = Column(Float)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    cost_breakdown = relationship("CostBreakdown", back_populates="analysis")

class CostBreakdown(Base):
    """Model for storing detailed cost breakdown"""
    __tablename__ = "cost_breakdowns"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("damage_analyses.id"))
    
    # Cost components
    parts_cost = Column(Float, default=0.0)
    labor_cost = Column(Float, default=0.0)
    paint_cost = Column(Float, default=0.0)
    tax_amount = Column(Float, default=0.0)
    total_cost = Column(Float, default=0.0)
    
    # Detailed breakdown
    cost_details = Column(JSON)  # Detailed cost breakdown by damage type
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    analysis = relationship("DamageAnalysis", back_populates="cost_breakdown")

class CarPart(Base):
    """Model for car parts and their prices"""
    __tablename__ = "car_parts"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    category = Column(String, nullable=False)  # bumper, headlight, mirror, etc.
    
    # Pricing
    price_tnd = Column(Float, nullable=False)
    labor_hours = Column(Float, default=1.0)  # Estimated labor hours for replacement
    
    # Car compatibility
    compatible_makes = Column(JSON)  # List of compatible car makes
    compatible_models = Column(JSON)  # List of compatible car models
    
    # Metadata
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class RepairType(Base):
    """Model for different types of repairs and their costs"""
    __tablename__ = "repair_types"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, unique=True)
    description = Column(Text)
    
    # Cost factors
    base_cost_tnd = Column(Float, nullable=False)
    labor_hours = Column(Float, nullable=False)
    difficulty_multiplier = Column(Float, default=1.0)
    
    # Damage type mapping
    damage_type = Column(String, nullable=False)  # Maps to DAMAGE_TYPES in config
    severity_level = Column(String, nullable=False)  # minor, moderate, severe
    
    # Metadata
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class InsuranceReport(Base):
    """Model for insurance reports"""
    __tablename__ = "insurance_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    analysis_id = Column(Integer, ForeignKey("damage_analyses.id"))
    
    # Report details
    report_number = Column(String, unique=True, index=True)
    vehicle_info = Column(JSON)  # Make, model, year, VIN, etc.
    incident_details = Column(JSON)  # Date, location, description
    
    # Assessment
    damage_summary = Column(Text)
    repair_recommendations = Column(JSON)
    estimated_repair_time = Column(Integer)  # Days
    
    # Status
    status = Column(String, default="draft")  # draft, submitted, approved, rejected
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    analysis = relationship("DamageAnalysis")
