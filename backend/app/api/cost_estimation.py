"""
API endpoints for cost estimation
"""
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import Dict, Any

from app.core.database import get_db
from app.services.cost_estimation import CostEstimationService
from app.models.schemas import CostBreakdownResponse
from app.models.database import DamageAnalysis, CostBreakdown

router = APIRouter()

@router.get("/cost-breakdown/{analysis_id}", response_model=CostBreakdownResponse)
async def get_cost_breakdown(analysis_id: int, db: Session = Depends(get_db)):
    """
    Get detailed cost breakdown for a damage analysis
    """
    try:
        # Get the analysis
        analysis = db.query(DamageAnalysis).filter(DamageAnalysis.id == analysis_id).first()
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        # Get the cost breakdown
        cost_breakdown = db.query(CostBreakdown).filter(CostBreakdown.analysis_id == analysis_id).first()
        if not cost_breakdown:
            raise HTTPException(status_code=404, detail="Cost breakdown not found")
        
        # Convert cost details back to schema objects
        from app.models.schemas import CostBreakdownItem
        cost_details = [CostBreakdownItem(**item) for item in cost_breakdown.cost_details]
        
        return CostBreakdownResponse(
            analysis_id=cost_breakdown.analysis_id,
            parts_cost=cost_breakdown.parts_cost,
            labor_cost=cost_breakdown.labor_cost,
            paint_cost=cost_breakdown.paint_cost,
            tax_amount=cost_breakdown.tax_amount,
            total_cost=cost_breakdown.total_cost,
            cost_details=cost_details,
            currency="TND"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving cost breakdown: {str(e)}")

@router.post("/recalculate-cost/{analysis_id}", response_model=CostBreakdownResponse)
async def recalculate_cost(
    analysis_id: int,
    vehicle_info: Dict[str, Any] = None,
    db: Session = Depends(get_db)
):
    """
    Recalculate cost estimation with updated parameters
    """
    try:
        # Get the analysis
        analysis = db.query(DamageAnalysis).filter(DamageAnalysis.id == analysis_id).first()
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        # Convert stored damages back to schema objects
        from app.models.schemas import DetectedDamage
        detected_damages = [DetectedDamage(**damage) for damage in analysis.detected_damages]
        
        # Recalculate costs
        cost_service = CostEstimationService(db)
        cost_breakdown = cost_service.estimate_cost(detected_damages, vehicle_info)
        
        # Update database
        db_cost_breakdown = db.query(CostBreakdown).filter(CostBreakdown.analysis_id == analysis_id).first()
        if db_cost_breakdown:
            db_cost_breakdown.parts_cost = cost_breakdown.parts_cost
            db_cost_breakdown.labor_cost = cost_breakdown.labor_cost
            db_cost_breakdown.paint_cost = cost_breakdown.paint_cost
            db_cost_breakdown.tax_amount = cost_breakdown.tax_amount
            db_cost_breakdown.total_cost = cost_breakdown.total_cost
            db_cost_breakdown.cost_details = [item.dict() for item in cost_breakdown.cost_details]
        else:
            db_cost_breakdown = CostBreakdown(
                analysis_id=analysis_id,
                parts_cost=cost_breakdown.parts_cost,
                labor_cost=cost_breakdown.labor_cost,
                paint_cost=cost_breakdown.paint_cost,
                tax_amount=cost_breakdown.tax_amount,
                total_cost=cost_breakdown.total_cost,
                cost_details=[item.dict() for item in cost_breakdown.cost_details]
            )
            db.add(db_cost_breakdown)
        
        # Update total cost in analysis
        analysis.total_estimated_cost = cost_breakdown.total_cost
        
        db.commit()
        
        cost_breakdown.analysis_id = analysis_id
        return cost_breakdown
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error recalculating cost: {str(e)}")

@router.get("/market-prices")
async def get_market_prices(db: Session = Depends(get_db)):
    """
    Get current market prices for car parts
    """
    try:
        from app.models.database import CarPart
        
        parts = db.query(CarPart).filter(CarPart.is_active == True).all()
        
        market_prices = {}
        for part in parts:
            market_prices[part.name] = {
                "price_tnd": part.price_tnd,
                "labor_hours": part.labor_hours,
                "category": part.category,
                "compatible_makes": part.compatible_makes,
                "compatible_models": part.compatible_models
            }
        
        return {"market_prices": market_prices, "currency": "TND"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving market prices: {str(e)}")

@router.post("/update-market-prices")
async def update_market_prices(
    price_updates: Dict[str, float],
    db: Session = Depends(get_db)
):
    """
    Update market prices for car parts (admin only)
    """
    try:
        cost_service = CostEstimationService(db)
        success = cost_service.update_market_prices(price_updates)
        
        if success:
            return {"message": "Market prices updated successfully", "updated_items": len(price_updates)}
        else:
            raise HTTPException(status_code=500, detail="Failed to update market prices")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating market prices: {str(e)}")

@router.get("/cost-summary/{analysis_id}")
async def get_cost_summary(analysis_id: int, db: Session = Depends(get_db)):
    """
    Get a simplified cost summary for quick reference
    """
    try:
        analysis = db.query(DamageAnalysis).filter(DamageAnalysis.id == analysis_id).first()
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        cost_breakdown = db.query(CostBreakdown).filter(CostBreakdown.analysis_id == analysis_id).first()
        if not cost_breakdown:
            raise HTTPException(status_code=404, detail="Cost breakdown not found")
        
        return {
            "analysis_id": analysis_id,
            "total_cost": cost_breakdown.total_cost,
            "currency": "TND",
            "breakdown": {
                "parts": cost_breakdown.parts_cost,
                "labor": cost_breakdown.labor_cost,
                "paint": cost_breakdown.paint_cost,
                "tax": cost_breakdown.tax_amount
            },
            "damage_count": len(analysis.detected_damages),
            "severity": analysis.overall_severity
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving cost summary: {str(e)}")
