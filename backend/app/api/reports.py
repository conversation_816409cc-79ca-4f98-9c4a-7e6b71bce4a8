"""
API endpoints for insurance reports generation
"""
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
import uuid
from datetime import datetime
from typing import List

from app.core.database import get_db
from app.models.schemas import (
    InsuranceReportRequest, InsuranceReportResponse, 
    RepairRecommendation, DamageType
)
from app.models.database import DamageAnalysis, CostBreakdown, InsuranceReport

router = APIRouter()

@router.post("/generate-report", response_model=InsuranceReportResponse)
async def generate_insurance_report(
    report_request: InsuranceReportRequest,
    db: Session = Depends(get_db)
):
    """
    Generate insurance report from damage analysis
    """
    try:
        # Get the analysis
        analysis = db.query(DamageAnalysis).filter(
            DamageAnalysis.id == report_request.analysis_id
        ).first()
        
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        # Get cost breakdown
        cost_breakdown = db.query(CostBreakdown).filter(
            CostBreakdown.analysis_id == report_request.analysis_id
        ).first()
        
        if not cost_breakdown:
            raise HTTPException(status_code=404, detail="Cost breakdown not found")
        
        # Generate report number
        report_number = f"RPT-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        
        # Generate damage summary
        damage_summary = _generate_damage_summary(analysis.detected_damages)
        
        # Generate repair recommendations
        repair_recommendations = _generate_repair_recommendations(
            analysis.detected_damages, 
            cost_breakdown.cost_details
        )
        
        # Estimate repair time
        estimated_repair_time = _estimate_repair_time(analysis.detected_damages)
        
        # Create insurance report
        db_report = InsuranceReport(
            analysis_id=report_request.analysis_id,
            report_number=report_number,
            vehicle_info=report_request.vehicle_info.dict(),
            incident_details=report_request.incident_details.dict(),
            damage_summary=damage_summary,
            repair_recommendations=[rec.dict() for rec in repair_recommendations],
            estimated_repair_time=estimated_repair_time,
            status="draft"
        )
        
        db.add(db_report)
        db.commit()
        db.refresh(db_report)
        
        return InsuranceReportResponse(
            report_id=db_report.id,
            report_number=report_number,
            analysis_id=report_request.analysis_id,
            vehicle_info=report_request.vehicle_info,
            incident_details=report_request.incident_details,
            damage_summary=damage_summary,
            repair_recommendations=repair_recommendations,
            total_cost_estimate=cost_breakdown.total_cost,
            estimated_repair_time=estimated_repair_time,
            status="draft",
            created_at=db_report.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating report: {str(e)}")

@router.get("/report/{report_id}", response_model=InsuranceReportResponse)
async def get_insurance_report(report_id: int, db: Session = Depends(get_db)):
    """
    Get insurance report by ID
    """
    try:
        report = db.query(InsuranceReport).filter(InsuranceReport.id == report_id).first()
        
        if not report:
            raise HTTPException(status_code=404, detail="Report not found")
        
        # Get cost breakdown for total cost
        cost_breakdown = db.query(CostBreakdown).filter(
            CostBreakdown.analysis_id == report.analysis_id
        ).first()
        
        total_cost = cost_breakdown.total_cost if cost_breakdown else 0.0
        
        # Convert stored data back to schema objects
        from app.models.schemas import VehicleInfo, IncidentDetails, RepairRecommendation
        
        vehicle_info = VehicleInfo(**report.vehicle_info)
        incident_details = IncidentDetails(**report.incident_details)
        repair_recommendations = [RepairRecommendation(**rec) for rec in report.repair_recommendations]
        
        return InsuranceReportResponse(
            report_id=report.id,
            report_number=report.report_number,
            analysis_id=report.analysis_id,
            vehicle_info=vehicle_info,
            incident_details=incident_details,
            damage_summary=report.damage_summary,
            repair_recommendations=repair_recommendations,
            total_cost_estimate=total_cost,
            estimated_repair_time=report.estimated_repair_time,
            status=report.status,
            created_at=report.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving report: {str(e)}")

@router.put("/report/{report_id}/status")
async def update_report_status(
    report_id: int,
    status: str,
    db: Session = Depends(get_db)
):
    """
    Update insurance report status
    """
    try:
        valid_statuses = ["draft", "submitted", "approved", "rejected"]
        if status not in valid_statuses:
            raise HTTPException(status_code=400, detail=f"Invalid status. Must be one of: {valid_statuses}")
        
        report = db.query(InsuranceReport).filter(InsuranceReport.id == report_id).first()
        
        if not report:
            raise HTTPException(status_code=404, detail="Report not found")
        
        report.status = status
        db.commit()
        
        return {"message": f"Report status updated to {status}", "report_id": report_id}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error updating report status: {str(e)}")

@router.get("/reports")
async def list_reports(
    skip: int = 0,
    limit: int = 100,
    status: str = None,
    db: Session = Depends(get_db)
):
    """
    List insurance reports with optional filtering
    """
    try:
        query = db.query(InsuranceReport)
        
        if status:
            query = query.filter(InsuranceReport.status == status)
        
        reports = query.offset(skip).limit(limit).all()
        
        return {
            "reports": [
                {
                    "report_id": report.id,
                    "report_number": report.report_number,
                    "analysis_id": report.analysis_id,
                    "status": report.status,
                    "created_at": report.created_at,
                    "vehicle_make": report.vehicle_info.get("make"),
                    "vehicle_model": report.vehicle_info.get("model")
                }
                for report in reports
            ],
            "total": len(reports)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing reports: {str(e)}")

def _generate_damage_summary(detected_damages: List[dict]) -> str:
    """Generate a human-readable damage summary"""
    if not detected_damages:
        return "No significant damage detected."
    
    damage_counts = {}
    for damage in detected_damages:
        damage_type = damage.get("damage_type", "unknown")
        severity = damage.get("severity", "unknown")
        key = f"{severity} {damage_type.replace('_', ' ')}"
        damage_counts[key] = damage_counts.get(key, 0) + 1
    
    summary_parts = []
    for damage_desc, count in damage_counts.items():
        if count == 1:
            summary_parts.append(f"1 {damage_desc}")
        else:
            summary_parts.append(f"{count} {damage_desc}s")
    
    return f"Vehicle shows {', '.join(summary_parts)}."

def _generate_repair_recommendations(detected_damages: List[dict], cost_details: List[dict]) -> List[RepairRecommendation]:
    """Generate repair recommendations based on detected damages"""
    recommendations = []
    
    for i, damage in enumerate(detected_damages):
        damage_type = DamageType(damage.get("damage_type"))
        severity = damage.get("severity", "minor")
        confidence = damage.get("confidence", 0.0)
        
        # Determine recommended action
        if severity == "severe" or confidence > 0.8:
            action = "replace" if damage_type in [DamageType.BROKEN_BUMPER, DamageType.BROKEN_HEADLIGHT] else "repair"
            priority = "high"
        elif severity == "moderate":
            action = "repair"
            priority = "medium"
        else:
            action = "repair" if confidence > 0.5 else "monitor"
            priority = "low"
        
        # Estimate time
        time_estimates = {
            "minor": 2, "moderate": 4, "severe": 8
        }
        estimated_time = time_estimates.get(severity, 4)
        
        # Get cost from cost details
        cost_estimate = 0.0
        if i < len(cost_details):
            cost_estimate = cost_details[i].get("total_cost", 0.0)
        
        recommendation = RepairRecommendation(
            damage_type=damage_type,
            recommended_action=action,
            priority=priority,
            estimated_time=estimated_time,
            cost_estimate=cost_estimate,
            notes=f"Confidence: {confidence:.2f}, Severity: {severity}"
        )
        
        recommendations.append(recommendation)
    
    return recommendations

def _estimate_repair_time(detected_damages: List[dict]) -> int:
    """Estimate total repair time in days"""
    if not detected_damages:
        return 0
    
    total_hours = 0
    for damage in detected_damages:
        severity = damage.get("severity", "minor")
        damage_type = damage.get("damage_type", "")
        
        # Base hours by severity
        base_hours = {"minor": 2, "moderate": 4, "severe": 8}
        hours = base_hours.get(severity, 4)
        
        # Adjust for damage type
        if "structural" in damage_type:
            hours *= 2
        elif "broken" in damage_type:
            hours *= 1.5
        
        total_hours += hours
    
    # Convert to days (8 hours per day)
    days = max(1, total_hours // 8)
    return int(days)
