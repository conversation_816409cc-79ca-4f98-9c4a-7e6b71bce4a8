"""
API endpoints for damage detection
"""
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from sqlalchemy.orm import Session
import os
import uuid
import time
from pathlib import Path
from typing import Optional

from app.core.database import get_db
from app.services.damage_detection import DamageDetectionService
from app.services.cost_estimation import CostEstimationService
from app.models.schemas import DamageAnalysisResponse, DamageAnalysisRequest
from app.models.database import DamageAnalysis, CostBreakdown
from app.core.config import settings

router = APIRouter()

# Initialize services
damage_service = DamageDetectionService()

@router.post("/analyze-damage", response_model=DamageAnalysisResponse)
async def analyze_damage(
    image: UploadFile = File(...),
    vehicle_make: Optional[str] = Form(None),
    vehicle_model: Optional[str] = Form(None),
    vehicle_year: Optional[int] = Form(None),
    db: Session = Depends(get_db)
):
    """
    Analyze car damage from uploaded image
    """
    try:
        # Validate file
        if not image.content_type.startswith("image/"):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Check file size
        if image.size > settings.MAX_FILE_SIZE:
            raise HTTPException(status_code=400, detail="File too large")
        
        # Create upload directory if it doesn't exist
        upload_dir = Path(settings.UPLOAD_DIR)
        upload_dir.mkdir(exist_ok=True)
        
        # Save uploaded file
        file_extension = Path(image.filename).suffix.lower()
        if file_extension not in settings.ALLOWED_EXTENSIONS:
            raise HTTPException(status_code=400, detail="File type not allowed")
        
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / unique_filename
        
        with open(file_path, "wb") as buffer:
            content = await image.read()
            buffer.write(content)
        
        # Detect damages
        start_time = time.time()
        detected_damages = damage_service.detect_damages(str(file_path))
        
        # Get overall severity
        overall_severity = damage_service.get_overall_severity(detected_damages)
        
        # Estimate costs
        cost_service = CostEstimationService(db)
        vehicle_info = {
            "make": vehicle_make,
            "model": vehicle_model,
            "year": vehicle_year
        } if vehicle_make else None
        
        cost_breakdown = cost_service.estimate_cost(detected_damages, vehicle_info)
        
        processing_time = time.time() - start_time
        
        # Calculate overall confidence
        if detected_damages:
            overall_confidence = sum(d.confidence for d in detected_damages) / len(detected_damages)
        else:
            overall_confidence = 0.0
        
        # Save to database
        db_analysis = DamageAnalysis(
            image_path=str(file_path),
            original_filename=image.filename,
            detected_damages=[d.dict() for d in detected_damages],
            confidence_scores=[d.confidence for d in detected_damages],
            overall_severity=overall_severity.value,
            total_estimated_cost=cost_breakdown.total_cost
        )
        
        db.add(db_analysis)
        db.commit()
        db.refresh(db_analysis)
        
        # Save cost breakdown
        db_cost_breakdown = CostBreakdown(
            analysis_id=db_analysis.id,
            parts_cost=cost_breakdown.parts_cost,
            labor_cost=cost_breakdown.labor_cost,
            paint_cost=cost_breakdown.paint_cost,
            tax_amount=cost_breakdown.tax_amount,
            total_cost=cost_breakdown.total_cost,
            cost_details=[item.dict() for item in cost_breakdown.cost_details]
        )
        
        db.add(db_cost_breakdown)
        db.commit()
        
        return DamageAnalysisResponse(
            analysis_id=db_analysis.id,
            detected_damages=detected_damages,
            overall_severity=overall_severity,
            total_estimated_cost=cost_breakdown.total_cost,
            confidence_score=overall_confidence,
            processing_time=processing_time,
            created_at=db_analysis.created_at
        )
        
    except Exception as e:
        # Clean up uploaded file on error
        if 'file_path' in locals() and file_path.exists():
            file_path.unlink()
        
        raise HTTPException(status_code=500, detail=f"Error analyzing damage: {str(e)}")

@router.get("/analysis/{analysis_id}", response_model=DamageAnalysisResponse)
async def get_analysis(analysis_id: int, db: Session = Depends(get_db)):
    """
    Get damage analysis by ID
    """
    try:
        analysis = db.query(DamageAnalysis).filter(DamageAnalysis.id == analysis_id).first()
        
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        # Convert stored data back to schema objects
        from app.models.schemas import DetectedDamage, SeverityLevel
        detected_damages = [DetectedDamage(**damage) for damage in analysis.detected_damages]
        
        overall_confidence = sum(analysis.confidence_scores) / len(analysis.confidence_scores) if analysis.confidence_scores else 0.0
        
        return DamageAnalysisResponse(
            analysis_id=analysis.id,
            detected_damages=detected_damages,
            overall_severity=SeverityLevel(analysis.overall_severity),
            total_estimated_cost=analysis.total_estimated_cost,
            confidence_score=overall_confidence,
            processing_time=0.0,  # Not stored, so return 0
            created_at=analysis.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving analysis: {str(e)}")

@router.delete("/analysis/{analysis_id}")
async def delete_analysis(analysis_id: int, db: Session = Depends(get_db)):
    """
    Delete damage analysis and associated files
    """
    try:
        analysis = db.query(DamageAnalysis).filter(DamageAnalysis.id == analysis_id).first()
        
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        # Delete associated file
        if analysis.image_path and Path(analysis.image_path).exists():
            Path(analysis.image_path).unlink()
        
        # Delete from database
        db.delete(analysis)
        db.commit()
        
        return {"message": "Analysis deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error deleting analysis: {str(e)}")
