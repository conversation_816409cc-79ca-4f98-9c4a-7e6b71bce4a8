"""
Service for estimating repair costs based on detected damages and Tunisian market prices
"""
import logging
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.models.schemas import (
    DetectedDamage, DamageType, SeverityLevel, 
    CostBreakdownResponse, CostBreakdownItem
)
from app.models.database import CarPart, RepairType
from app.core.config import settings

logger = logging.getLogger(__name__)

class CostEstimationService:
    """Service for estimating repair costs"""
    
    def __init__(self, db: Session):
        self.db = db
        self.currency = settings.CURRENCY
        self.tax_rate = settings.TAX_RATE
        self.labor_rate = settings.LABOR_RATE_PER_HOUR
        
        # Tunisian market price data (in TND)
        self.base_prices = {
            DamageType.SCRATCH_SURFACE: {"minor": 50, "moderate": 120, "severe": 250},
            DamageType.SCRATCH_DEEP: {"minor": 150, "moderate": 300, "severe": 500},
            DamageType.DENT_MINOR: {"minor": 80, "moderate": 180, "severe": 350},
            DamageType.DENT_MAJOR: {"minor": 200, "moderate": 400, "severe": 800},
            DamageType.BROKEN_BUMPER: {"minor": 300, "moderate": 600, "severe": 1200},
            DamageType.BROKEN_HEADLIGHT: {"minor": 150, "moderate": 300, "severe": 600},
            DamageType.BROKEN_MIRROR: {"minor": 80, "moderate": 150, "severe": 300},
            DamageType.PAINT_DAMAGE: {"minor": 100, "moderate": 250, "severe": 500},
            DamageType.GLASS_DAMAGE: {"minor": 200, "moderate": 400, "severe": 800},
            DamageType.STRUCTURAL_DAMAGE: {"minor": 500, "moderate": 1500, "severe": 3000}
        }
        
        # Labor hours estimation
        self.labor_hours = {
            DamageType.SCRATCH_SURFACE: {"minor": 1, "moderate": 2, "severe": 4},
            DamageType.SCRATCH_DEEP: {"minor": 2, "moderate": 4, "severe": 6},
            DamageType.DENT_MINOR: {"minor": 1, "moderate": 2, "severe": 3},
            DamageType.DENT_MAJOR: {"minor": 2, "moderate": 4, "severe": 8},
            DamageType.BROKEN_BUMPER: {"minor": 2, "moderate": 4, "severe": 6},
            DamageType.BROKEN_HEADLIGHT: {"minor": 1, "moderate": 2, "severe": 3},
            DamageType.BROKEN_MIRROR: {"minor": 0.5, "moderate": 1, "severe": 2},
            DamageType.PAINT_DAMAGE: {"minor": 2, "moderate": 4, "severe": 8},
            DamageType.GLASS_DAMAGE: {"minor": 1, "moderate": 2, "severe": 4},
            DamageType.STRUCTURAL_DAMAGE: {"minor": 8, "moderate": 16, "severe": 32}
        }
    
    def estimate_cost(self, damages: List[DetectedDamage], vehicle_info: Optional[Dict[str, Any]] = None) -> CostBreakdownResponse:
        """Estimate total repair cost for detected damages"""
        try:
            cost_details = []
            total_parts_cost = 0.0
            total_labor_cost = 0.0
            total_paint_cost = 0.0
            
            for damage in damages:
                # Get base cost for this damage type and severity
                parts_cost = self._get_parts_cost(damage, vehicle_info)
                labor_hours = self._get_labor_hours(damage)
                labor_cost = labor_hours * self.labor_rate
                paint_cost = self._get_paint_cost(damage)
                
                # Apply confidence factor
                confidence_factor = min(damage.confidence * 1.2, 1.0)  # Boost confidence slightly
                parts_cost *= confidence_factor
                labor_cost *= confidence_factor
                paint_cost *= confidence_factor
                
                # Apply area factor for larger damages
                area_factor = 1.0 + (damage.affected_area / 100) * 0.5
                parts_cost *= area_factor
                labor_cost *= area_factor
                
                # Add to totals
                total_parts_cost += parts_cost
                total_labor_cost += labor_cost
                total_paint_cost += paint_cost
                
                # Create cost breakdown item
                item_total = parts_cost + labor_cost + paint_cost
                cost_details.append(CostBreakdownItem(
                    item_name=f"{damage.damage_type.value.replace('_', ' ').title()}",
                    item_type="repair",
                    quantity=1.0,
                    unit_cost=item_total,
                    total_cost=item_total,
                    description=f"{damage.severity.value} {damage.damage_type.value} (confidence: {damage.confidence:.2f})"
                ))
            
            # Calculate tax
            subtotal = total_parts_cost + total_labor_cost + total_paint_cost
            tax_amount = subtotal * self.tax_rate
            total_cost = subtotal + tax_amount
            
            return CostBreakdownResponse(
                analysis_id=0,  # Will be set by the calling function
                parts_cost=round(total_parts_cost, 2),
                labor_cost=round(total_labor_cost, 2),
                paint_cost=round(total_paint_cost, 2),
                tax_amount=round(tax_amount, 2),
                total_cost=round(total_cost, 2),
                cost_details=cost_details,
                currency=self.currency
            )
            
        except Exception as e:
            logger.error(f"Error estimating cost: {e}")
            raise
    
    def _get_parts_cost(self, damage: DetectedDamage, vehicle_info: Optional[Dict[str, Any]] = None) -> float:
        """Get parts cost for specific damage"""
        base_cost = self.base_prices.get(damage.damage_type, {}).get(damage.severity.value, 100)
        
        # Apply vehicle-specific multipliers
        if vehicle_info:
            make = vehicle_info.get("make", "").lower()
            year = vehicle_info.get("year", 2020)
            
            # Luxury car multiplier
            luxury_makes = ["bmw", "mercedes", "audi", "lexus", "jaguar", "porsche"]
            if make in luxury_makes:
                base_cost *= 1.5
            
            # Age factor (older cars might have cheaper parts)
            current_year = 2024
            age = current_year - year
            if age > 10:
                base_cost *= 0.8
            elif age > 5:
                base_cost *= 0.9
        
        return base_cost
    
    def _get_labor_hours(self, damage: DetectedDamage) -> float:
        """Get estimated labor hours for specific damage"""
        return self.labor_hours.get(damage.damage_type, {}).get(damage.severity.value, 2.0)
    
    def _get_paint_cost(self, damage: DetectedDamage) -> float:
        """Get paint cost for damages that require painting"""
        paint_required_damages = [
            DamageType.SCRATCH_DEEP,
            DamageType.DENT_MAJOR,
            DamageType.PAINT_DAMAGE,
            DamageType.BROKEN_BUMPER
        ]
        
        if damage.damage_type in paint_required_damages:
            base_paint_cost = {
                "minor": 50,
                "moderate": 100,
                "severe": 200
            }
            return base_paint_cost.get(damage.severity.value, 100)
        
        return 0.0
    
    def get_market_price(self, part_name: str, make: str = None) -> Optional[float]:
        """Get market price for specific car part"""
        try:
            query = self.db.query(CarPart).filter(CarPart.name.ilike(f"%{part_name}%"))
            
            if make:
                query = query.filter(CarPart.compatible_makes.contains([make.lower()]))
            
            part = query.first()
            return part.price_tnd if part else None
            
        except Exception as e:
            logger.error(f"Error getting market price: {e}")
            return None
    
    def update_market_prices(self, price_updates: Dict[str, float]) -> bool:
        """Update market prices for car parts"""
        try:
            for part_name, new_price in price_updates.items():
                part = self.db.query(CarPart).filter(CarPart.name.ilike(f"%{part_name}%")).first()
                if part:
                    part.price_tnd = new_price
                    self.db.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating market prices: {e}")
            self.db.rollback()
            return False
