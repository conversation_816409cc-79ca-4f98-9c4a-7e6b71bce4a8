"""
AI service for car damage detection using YOLO and computer vision
"""
import cv2
import numpy as np
import torch
from ultralytics import <PERSON>OL<PERSON>
from PIL import Image
import logging
from typing import List, Tuple, Dict, Any
from pathlib import Path
import time

from app.models.schemas import DetectedDamage, DamageType, SeverityLevel
from app.core.config import settings

logger = logging.getLogger(__name__)

class DamageDetectionService:
    """Service for detecting and classifying car damage"""
    
    def __init__(self):
        self.model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.damage_classes = {
            0: DamageType.SCRATCH_SURFACE,
            1: DamageType.SCRATCH_DEEP,
            2: DamageType.DENT_MINOR,
            3: DamageType.DENT_MAJOR,
            4: DamageType.BROKEN_BUMPER,
            5: DamageType.BROKEN_HEADLIGHT,
            6: DamageType.BROKEN_MIRROR,
            7: DamageType.PAINT_DAMAGE,
            8: DamageType.GLASS_DAMAGE,
            9: DamageType.STRUCTURAL_DAMAGE
        }
        self.load_model()
    
    def load_model(self):
        """Load the YOLO model for damage detection"""
        try:
            model_path = Path(settings.DAMAGE_DETECTION_MODEL_PATH)
            if model_path.exists():
                self.model = YOLO(str(model_path))
                logger.info(f"Loaded damage detection model from {model_path}")
            else:
                # Use pre-trained YOLO model and fine-tune later
                self.model = YOLO('yolov8n.pt')
                logger.warning(f"Model not found at {model_path}, using pre-trained YOLOv8")
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            self.model = YOLO('yolov8n.pt')
    
    def preprocess_image(self, image_path: str) -> np.ndarray:
        """Preprocess image for damage detection"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Could not load image from {image_path}")
            
            # Convert BGR to RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Resize image while maintaining aspect ratio
            height, width = image.shape[:2]
            max_size = 640
            
            if max(height, width) > max_size:
                scale = max_size / max(height, width)
                new_width = int(width * scale)
                new_height = int(height * scale)
                image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            
            return image
        except Exception as e:
            logger.error(f"Error preprocessing image: {e}")
            raise
    
    def detect_damages(self, image_path: str) -> List[DetectedDamage]:
        """Detect damages in the given image"""
        try:
            start_time = time.time()
            
            # Preprocess image
            image = self.preprocess_image(image_path)
            
            # Run inference
            results = self.model(image)
            
            detected_damages = []
            
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Extract detection data
                        confidence = float(box.conf[0])
                        class_id = int(box.cls[0])
                        bbox = box.xyxy[0].tolist()  # [x1, y1, x2, y2]
                        
                        # Skip low confidence detections
                        if confidence < 0.3:
                            continue
                        
                        # Map class ID to damage type (for now, use mock mapping)
                        damage_type = self._map_class_to_damage_type(class_id)
                        
                        # Estimate severity based on bounding box size and confidence
                        severity = self._estimate_severity(bbox, confidence, image.shape)
                        
                        # Calculate affected area
                        affected_area = self._calculate_affected_area(bbox, image.shape)
                        
                        detected_damage = DetectedDamage(
                            damage_type=damage_type,
                            severity=severity,
                            confidence=confidence,
                            bounding_box=bbox,
                            affected_area=affected_area,
                            description=f"{damage_type.value} detected with {confidence:.2f} confidence"
                        )
                        
                        detected_damages.append(detected_damage)
            
            processing_time = time.time() - start_time
            logger.info(f"Detected {len(detected_damages)} damages in {processing_time:.2f}s")
            
            return detected_damages
            
        except Exception as e:
            logger.error(f"Error detecting damages: {e}")
            raise
    
    def _map_class_to_damage_type(self, class_id: int) -> DamageType:
        """Map YOLO class ID to damage type"""
        # For now, use a simple mapping. In production, this would be based on trained model
        damage_mapping = {
            0: DamageType.SCRATCH_SURFACE,
            1: DamageType.DENT_MINOR,
            2: DamageType.BROKEN_BUMPER,
            3: DamageType.PAINT_DAMAGE,
            # Add more mappings based on your trained model
        }
        return damage_mapping.get(class_id % len(damage_mapping), DamageType.SCRATCH_SURFACE)
    
    def _estimate_severity(self, bbox: List[float], confidence: float, image_shape: Tuple[int, int, int]) -> SeverityLevel:
        """Estimate damage severity based on size and confidence"""
        x1, y1, x2, y2 = bbox
        width = x2 - x1
        height = y2 - y1
        area = width * height
        
        # Calculate relative area
        image_area = image_shape[0] * image_shape[1]
        relative_area = area / image_area
        
        # Severity estimation logic
        if relative_area > 0.1 or confidence > 0.8:
            return SeverityLevel.SEVERE
        elif relative_area > 0.05 or confidence > 0.6:
            return SeverityLevel.MODERATE
        else:
            return SeverityLevel.MINOR
    
    def _calculate_affected_area(self, bbox: List[float], image_shape: Tuple[int, int, int]) -> float:
        """Calculate the percentage of affected area"""
        x1, y1, x2, y2 = bbox
        bbox_area = (x2 - x1) * (y2 - y1)
        image_area = image_shape[0] * image_shape[1]
        return (bbox_area / image_area) * 100
    
    def get_overall_severity(self, damages: List[DetectedDamage]) -> SeverityLevel:
        """Determine overall severity from individual damages"""
        if not damages:
            return SeverityLevel.MINOR
        
        severe_count = sum(1 for d in damages if d.severity == SeverityLevel.SEVERE)
        moderate_count = sum(1 for d in damages if d.severity == SeverityLevel.MODERATE)
        
        if severe_count > 0:
            return SeverityLevel.SEVERE
        elif moderate_count > 1 or (moderate_count == 1 and len(damages) > 2):
            return SeverityLevel.SEVERE
        elif moderate_count > 0:
            return SeverityLevel.MODERATE
        else:
            return SeverityLevel.MINOR
