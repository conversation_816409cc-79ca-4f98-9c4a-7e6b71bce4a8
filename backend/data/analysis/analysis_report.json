{"analysis_date": "2025-07-09T12:08:33.492501", "dataset_path": "../data/data.csv", "images_path": "../data/image", "results": {"class_distribution": {"counts": {"unknown": 549, "door_dent": 192, "bumper_scratch": 164, "door_scratch": 154, "glass_shatter": 137, "tail_lamp": 136, "head_lamp": 133, "bumper_dent": 129}, "percentages": {"unknown": 34.44, "door_dent": 12.05, "bumper_scratch": 10.29, "door_scratch": 9.66, "glass_shatter": 8.59, "tail_lamp": 8.53, "head_lamp": 8.34, "bumper_dent": 8.09}, "total_images": 1594, "unique_classes": 8}, "image_properties": {"statistics": {}, "missing_count": 100, "corrupted_count": 0, "sample_size": 100, "missing_images": ["image/917.jpeg", "image/1120.jpeg", "image/1276.jpeg", "image/361.jpeg", "image/1483.jpeg", "image/99.jpeg", "image/1209.jpeg", "image/554.jpeg", "image/1080.jpeg", "image/680.jpeg"], "corrupted_images": []}, "training_readiness": {"score": 85, "max_score": 100, "level": "Good", "issues": ["Moderate class imbalance (ratio: 4.3:1)", "Missing images detected (100.0%)"], "recommendations": ["Use weighted loss function or class balancing", "Clean dataset and fix missing images"]}}}