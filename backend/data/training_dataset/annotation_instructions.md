
# Vehicle Damage Annotation Instructions

## Dataset Information
- Total Images: 929
- Created: 20250708_124853
- Source: ../uploads/damaged_cars

## Damage Classes (YOLO Format)
0: scratch_surface
1: scratch_deep
2: dent_minor
3: dent_major
4: broken_bumper
5: broken_headlight
6: broken_mirror
7: paint_damage
8: glass_damage
9: structural_damage

## Annotation Process

### Step 1: Install LabelImg
```bash
pip install labelImg
```

### Step 2: Start Annotation
```bash
labelImg data/training_dataset/processed_images data/training_dataset/annotations
```

### Step 3: Annotation Guidelines
1. **Bounding Box**: Draw tight boxes around each damage area
2. **Class Selection**: Choose appropriate damage type from dropdown
3. **Multiple Damages**: Create separate boxes for each damage in same image
4. **Quality**: Only annotate clearly visible damages
5. **Consistency**: Use same criteria throughout

### Step 4: Save Format
- Format: YOLO
- Files saved to: data/training_dataset/annotations/
- Each image gets corresponding .txt file

## Damage Type Guidelines

### 0: scratch_surface
- Light surface scratches
- Can be polished out
- No primer/metal visible

### 1: scratch_deep  
- Deep scratches reaching primer/metal
- Cannot be polished out
- Requires touch-up paint

### 2: dent_minor
- Small dents < 5cm diameter
- No paint damage
- Can be pulled out

### 3: dent_major
- Large dents > 5cm diameter
- May have paint damage
- Requires body work

### 4: broken_bumper
- Cracked, broken, or detached bumper
- Missing bumper pieces
- Significant structural damage

### 5: broken_headlight
- Damaged headlight assembly
- Cracked lens or housing
- Missing headlight

### 6: broken_mirror
- Damaged side/rear view mirror
- Cracked mirror glass
- Missing mirror assembly

### 7: paint_damage
- Paint chips, fading, discoloration
- Rust spots
- Clear coat damage

### 8: glass_damage
- Cracked or broken windows
- Windshield damage
- Side window damage

### 9: structural_damage
- Frame or structural component damage
- Severe body damage
- Safety-critical damage

## Quality Control
- Annotate ALL visible damages in each image
- Review annotations before saving
- Maintain consistent labeling standards
- Skip unclear or ambiguous damages

## Next Steps After Annotation
1. Run: `python scripts/prepare_annotated_dataset.py`
2. Train model: `python scripts/train_small_dataset.py --data data/training_dataset`
