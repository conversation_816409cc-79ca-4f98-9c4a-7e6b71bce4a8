# Smart Car Damage Detection System - Setup Guide

## Overview
This guide will help you set up and run the Smart Car Damage Detection and Cost Estimation System for Tunisian Insurance Claims.

## Prerequisites

### System Requirements
- **Operating System**: Linux, macOS, or Windows 10/11
- **RAM**: Minimum 8GB (16GB recommended for AI processing)
- **Storage**: At least 10GB free space
- **GPU**: CUDA-compatible GPU recommended (optional but improves performance)

### Software Requirements
- **Docker**: Version 20.0 or higher
- **Docker Compose**: Version 2.0 or higher
- **Python**: 3.9 or higher (for development)
- **Node.js**: 16.0 or higher (for frontend development)
- **Git**: For version control

## Quick Start with Docker

### 1. Clone the Repository
```bash
git clone <repository-url>
cd car-damage-detection
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit the .env file with your settings
nano .env
```

### 3. Start the System
```bash
# Build and start all services
docker-compose up --build

# Or run in background
docker-compose up -d --build
```

### 4. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/api/docs
- **Database**: localhost:5432

### 5. Test the Setup
```bash
# Run the test script
python test_setup.py
```

## Development Setup

### Backend Development

1. **Create Virtual Environment**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Install Dependencies**
```bash
pip install -r requirements.txt
```

3. **Set Environment Variables**
```bash
export DATABASE_URL="postgresql://user:password@localhost:5432/car_damage_db"
export SECRET_KEY="your-secret-key"
```

4. **Run Database Migrations**
```bash
# Start PostgreSQL (if not using Docker)
# Then run the application to create tables
python -m app.main
```

5. **Start Development Server**
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Frontend Development

1. **Install Dependencies**
```bash
cd frontend
npm install
```

2. **Set Environment Variables**
```bash
# Create .env.local file
echo "REACT_APP_API_URL=http://localhost:8000" > .env.local
```

3. **Start Development Server**
```bash
npm start
```

## Configuration

### Environment Variables

#### Backend (.env)
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/car_damage_db

# Security
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application
DEBUG=True
ALLOWED_HOSTS=["http://localhost:3000"]

# AI Models
DAMAGE_DETECTION_MODEL_PATH=models/damage_detection.pt
COST_ESTIMATION_MODEL_PATH=models/cost_estimation.pkl

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_DIR=uploads

# Tunisian Market Settings
CURRENCY=TND
TAX_RATE=0.19
LABOR_RATE_PER_HOUR=25.0
```

#### Frontend (.env.local)
```bash
REACT_APP_API_URL=http://localhost:8000
```

### Database Configuration

The system uses PostgreSQL with the following default settings:
- **Host**: localhost
- **Port**: 5432
- **Database**: car_damage_db
- **Username**: user
- **Password**: password

## AI Models Setup

### Pre-trained Models
The system uses YOLO v8 for damage detection. On first run, it will download the base model automatically.

### Custom Model Training (Optional)
To train custom models with your own data:

1. **Prepare Dataset**
```bash
mkdir -p data/training/images
mkdir -p data/training/labels
# Add your annotated images and YOLO format labels
```

2. **Train Model**
```bash
cd backend
python scripts/train_model.py --data data/training --epochs 100
```

## Deployment

### Production Deployment with Docker

1. **Update Environment**
```bash
# Set production environment variables
export DEBUG=False
export SECRET_KEY="your-production-secret-key"
export DATABASE_URL="your-production-database-url"
```

2. **Build Production Images**
```bash
docker-compose -f docker-compose.prod.yml build
```

3. **Deploy**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Manual Deployment

#### Backend Deployment
```bash
# Install dependencies
pip install -r requirements.txt

# Set production environment
export PYTHONPATH=/path/to/app
export DATABASE_URL="your-production-db-url"

# Run with Gunicorn
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

#### Frontend Deployment
```bash
# Build for production
npm run build

# Serve with nginx or any static file server
# Copy build/ contents to your web server
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Error
```bash
# Check if PostgreSQL is running
docker-compose ps

# Check database logs
docker-compose logs db
```

#### 2. Frontend Build Errors
```bash
# Clear node modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### 3. AI Model Loading Issues
```bash
# Check if models directory exists
mkdir -p models

# Download models manually if needed
wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolov8n.pt -O models/yolov8n.pt
```

#### 4. Permission Issues
```bash
# Fix file permissions
chmod +x test_setup.py
sudo chown -R $USER:$USER uploads/
```

### Performance Optimization

#### GPU Support
To enable GPU acceleration:
```bash
# Install CUDA version of PyTorch
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

#### Memory Optimization
```bash
# Reduce batch size in config
export BATCH_SIZE=1

# Limit worker processes
export WORKERS=2
```

## Monitoring and Logs

### View Logs
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs backend
docker-compose logs frontend
docker-compose logs db
```

### Health Checks
```bash
# Backend health
curl http://localhost:8000/health

# Database health
docker-compose exec db pg_isready -U user -d car_damage_db
```

## Security Considerations

### Production Security
1. **Change default passwords**
2. **Use HTTPS in production**
3. **Set up proper firewall rules**
4. **Regular security updates**
5. **Backup database regularly**

### API Security
- JWT tokens for authentication
- Rate limiting on endpoints
- Input validation and sanitization
- CORS configuration

## Support

For technical support or questions:
1. Check the troubleshooting section above
2. Review application logs
3. Run the test script: `python test_setup.py`
4. Check GitHub issues for known problems

## License
This project is licensed under the MIT License.
