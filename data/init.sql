-- Initialize database with sample data for Car Damage Detection System

-- Insert sample car parts with Tunisian market prices
INSERT INTO car_parts (name, category, price_tnd, labor_hours, compatible_makes, compatible_models, is_active) VALUES
-- Bumpers
('Front Bumper - Standard', 'bumper', 450.00, 2.0, '["peugeot", "renault", "citroën"]', '["208", "clio", "c3"]', true),
('Front Bumper - Premium', 'bumper', 650.00, 2.5, '["bmw", "mercedes", "audi"]', '["3 series", "c-class", "a4"]', true),
('Rear Bumper - Standard', 'bumper', 380.00, 1.5, '["peugeot", "renault", "citroën"]', '["208", "clio", "c3"]', true),
('Rear Bumper - Premium', 'bumper', 580.00, 2.0, '["bmw", "mercedes", "audi"]', '["3 series", "c-class", "a4"]', true),

-- Headlights
('Headlight Assembly - Halogen', 'headlight', 180.00, 1.0, '["peugeot", "renault", "citroën", "volkswagen"]', '["208", "clio", "c3", "golf"]', true),
('Headlight Assembly - LED', 'headlight', 420.00, 1.5, '["bmw", "mercedes", "audi"]', '["3 series", "c-class", "a4"]', true),
('Headlight Lens', 'headlight', 85.00, 0.5, '["peugeot", "renault", "citroën"]', '["208", "clio", "c3"]', true),

-- Mirrors
('Side Mirror - Manual', 'mirror', 65.00, 0.5, '["peugeot", "renault", "citroën"]', '["208", "clio", "c3"]', true),
('Side Mirror - Electric', 'mirror', 120.00, 0.75, '["peugeot", "renault", "volkswagen"]', '["308", "megane", "golf"]', true),
('Side Mirror - Premium', 'mirror', 220.00, 1.0, '["bmw", "mercedes", "audi"]', '["3 series", "c-class", "a4"]', true),

-- Doors and Panels
('Door Panel - Front', 'panel', 320.00, 3.0, '["peugeot", "renault", "citroën"]', '["208", "clio", "c3"]', true),
('Door Panel - Rear', 'panel', 280.00, 2.5, '["peugeot", "renault", "citroën"]', '["208", "clio", "c3"]', true),
('Fender Panel', 'panel', 250.00, 2.0, '["peugeot", "renault", "citroën"]', '["208", "clio", "c3"]', true),

-- Glass
('Windshield - Standard', 'glass', 280.00, 2.0, '["peugeot", "renault", "citroën"]', '["208", "clio", "c3"]', true),
('Windshield - Premium', 'glass', 450.00, 2.5, '["bmw", "mercedes", "audi"]', '["3 series", "c-class", "a4"]', true),
('Side Window', 'glass', 120.00, 1.0, '["peugeot", "renault", "citroën"]', '["208", "clio", "c3"]', true),
('Rear Window', 'glass', 200.00, 1.5, '["peugeot", "renault", "citroën"]', '["208", "clio", "c3"]', true);

-- Insert repair types with Tunisian labor rates
INSERT INTO repair_types (name, description, base_cost_tnd, labor_hours, difficulty_multiplier, damage_type, severity_level, is_active) VALUES
-- Scratch repairs
('Surface Scratch Repair - Minor', 'Light surface scratch repair with polishing', 45.00, 1.0, 1.0, 'scratch_surface', 'minor', true),
('Surface Scratch Repair - Moderate', 'Multiple surface scratches requiring compound and polish', 95.00, 2.0, 1.2, 'scratch_surface', 'moderate', true),
('Surface Scratch Repair - Severe', 'Extensive surface damage requiring sanding and refinishing', 180.00, 4.0, 1.5, 'scratch_surface', 'severe', true),

('Deep Scratch Repair - Minor', 'Single deep scratch requiring touch-up paint', 120.00, 2.0, 1.3, 'scratch_deep', 'minor', true),
('Deep Scratch Repair - Moderate', 'Multiple deep scratches requiring primer and paint', 250.00, 4.0, 1.5, 'scratch_deep', 'moderate', true),
('Deep Scratch Repair - Severe', 'Extensive deep scratches requiring panel preparation and full repaint', 420.00, 6.0, 1.8, 'scratch_deep', 'severe', true),

-- Dent repairs
('Minor Dent Repair', 'Small dent removal using PDR techniques', 75.00, 1.0, 1.0, 'dent_minor', 'minor', true),
('Minor Dent Repair - Moderate', 'Multiple small dents requiring PDR and touch-up', 150.00, 2.0, 1.2, 'dent_minor', 'moderate', true),
('Minor Dent Repair - Severe', 'Large area with multiple small dents', 280.00, 3.0, 1.4, 'dent_minor', 'severe', true),

('Major Dent Repair - Minor', 'Single large dent requiring body filler', 180.00, 2.0, 1.4, 'dent_major', 'minor', true),
('Major Dent Repair - Moderate', 'Large dent requiring extensive bodywork', 350.00, 4.0, 1.6, 'dent_major', 'moderate', true),
('Major Dent Repair - Severe', 'Severe structural dent requiring panel replacement', 650.00, 8.0, 2.0, 'dent_major', 'severe', true),

-- Component replacements
('Bumper Replacement - Minor', 'Bumper replacement with minor modifications', 280.00, 2.0, 1.2, 'broken_bumper', 'minor', true),
('Bumper Replacement - Moderate', 'Bumper replacement with painting required', 450.00, 4.0, 1.4, 'broken_bumper', 'moderate', true),
('Bumper Replacement - Severe', 'Complete bumper system replacement with sensors', 750.00, 6.0, 1.8, 'broken_bumper', 'severe', true),

('Headlight Replacement - Minor', 'Single headlight lens replacement', 120.00, 1.0, 1.1, 'broken_headlight', 'minor', true),
('Headlight Replacement - Moderate', 'Complete headlight assembly replacement', 280.00, 2.0, 1.3, 'broken_headlight', 'moderate', true),
('Headlight Replacement - Severe', 'Headlight replacement with electrical work', 480.00, 3.0, 1.6, 'broken_headlight', 'severe', true),

('Mirror Replacement - Minor', 'Mirror glass replacement only', 45.00, 0.5, 1.0, 'broken_mirror', 'minor', true),
('Mirror Replacement - Moderate', 'Complete mirror assembly replacement', 95.00, 1.0, 1.2, 'broken_mirror', 'moderate', true),
('Mirror Replacement - Severe', 'Mirror replacement with electrical components', 180.00, 2.0, 1.5, 'broken_mirror', 'severe', true),

-- Paint damage
('Paint Touch-up - Minor', 'Small area paint touch-up', 85.00, 2.0, 1.1, 'paint_damage', 'minor', true),
('Paint Repair - Moderate', 'Panel section repainting', 220.00, 4.0, 1.4, 'paint_damage', 'moderate', true),
('Paint Restoration - Severe', 'Complete panel repainting with primer', 420.00, 8.0, 1.8, 'paint_damage', 'severe', true),

-- Glass damage
('Glass Chip Repair', 'Small chip or crack repair', 65.00, 1.0, 1.0, 'glass_damage', 'minor', true),
('Glass Panel Replacement - Moderate', 'Side or rear window replacement', 180.00, 2.0, 1.3, 'glass_damage', 'moderate', true),
('Windshield Replacement', 'Complete windshield replacement with calibration', 350.00, 4.0, 1.6, 'glass_damage', 'severe', true),

-- Structural damage
('Structural Assessment', 'Initial structural damage assessment', 150.00, 2.0, 1.5, 'structural_damage', 'minor', true),
('Structural Repair - Moderate', 'Frame straightening and reinforcement', 850.00, 16.0, 2.5, 'structural_damage', 'moderate', true),
('Structural Reconstruction', 'Major structural repair or replacement', 2200.00, 32.0, 3.0, 'structural_damage', 'severe', true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_car_parts_category ON car_parts(category);
CREATE INDEX IF NOT EXISTS idx_car_parts_active ON car_parts(is_active);
CREATE INDEX IF NOT EXISTS idx_repair_types_damage_severity ON repair_types(damage_type, severity_level);
CREATE INDEX IF NOT EXISTS idx_damage_analyses_created ON damage_analyses(created_at);
CREATE INDEX IF NOT EXISTS idx_insurance_reports_status ON insurance_reports(status);
