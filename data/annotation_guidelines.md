# Vehicle Damage Annotation Guidelines

## Overview
This guide provides detailed instructions for annotating vehicle damage images for training the AI model.

## Damage Categories and Class IDs

| Class ID | Damage Type | Description |
|----------|-------------|-------------|
| 0 | scratch_surface | Light surface scratches, can be polished out |
| 1 | scratch_deep | Deep scratches reaching primer/metal |
| 2 | dent_minor | Small dents < 5cm diameter |
| 3 | dent_major | Large dents > 5cm diameter |
| 4 | broken_bumper | Cracked, broken, or detached bumper |
| 5 | broken_headlight | Damaged headlight assembly |
| 6 | broken_mirror | Damaged side/rear view mirror |
| 7 | paint_damage | Paint chips, fading, or discoloration |
| 8 | glass_damage | Cracked or broken windows/windshield |
| 9 | structural_damage | Frame or structural component damage |

## Annotation Rules

### 1. Bounding Box Guidelines
- Draw tight bounding boxes around each damage area
- Include minimal background
- For multiple damages of same type: create separate boxes
- Minimum box size: 32x32 pixels

### 2. Quality Standards
- Only annotate clearly visible damage
- Skip blurry or unclear damage areas
- Ensure consistent labeling across similar damages
- Double-check class assignments

### 3. Severity Considerations
- Minor: < 25% of component affected
- Moderate: 25-75% of component affected  
- Severe: > 75% of component affected

### 4. Special Cases
- **Multiple overlapping damages**: Annotate the most severe type
- **Rust/corrosion**: Classify as paint_damage
- **Missing parts**: Classify based on what's missing (e.g., broken_bumper)
- **Reflection/lighting issues**: Skip if damage unclear

## File Naming Convention
```
images/: damage_001.jpg, damage_002.jpg, ...
labels/: damage_001.txt, damage_002.txt, ...
```

## YOLO Label Format
Each line in .txt file:
```
class_id center_x center_y width height
```
All coordinates normalized to 0-1 range.

## Quality Control
- Review 10% of annotations randomly
- Maintain annotation consistency log
- Regular team calibration sessions
