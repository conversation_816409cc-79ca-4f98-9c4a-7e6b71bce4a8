# Vehicle Damage Annotation Guidelines - Small Dataset (1,000 images)

## Overview
This guide provides detailed instructions for annotating vehicle damage images for training the AI model with a smaller dataset of ~1,000 images.

## Optimized Strategy for Small Dataset
- **Focus on quality over quantity**: Each annotation must be precise
- **Balanced distribution**: Aim for 100+ images per damage type
- **Multiple damages per image**: Annotate all visible damages in each image
- **Consistent labeling**: Use the same criteria throughout

## Damage Categories and Class IDs

| Class ID | Damage Type | Target Images | Description |
|----------|-------------|---------------|-------------|
| 0 | scratch_surface | 100-150 | Light surface scratches, can be polished out |
| 1 | scratch_deep | 100-150 | Deep scratches reaching primer/metal |
| 2 | dent_minor | 100-150 | Small dents < 5cm diameter |
| 3 | dent_major | 100-150 | Large dents > 5cm diameter |
| 4 | broken_bumper | 80-120 | Cracked, broken, or detached bumper |
| 5 | broken_headlight | 60-100 | Damaged headlight assembly |
| 6 | broken_mirror | 60-100 | Damaged side/rear view mirror |
| 7 | paint_damage | 100-150 | Paint chips, fading, or discoloration |
| 8 | glass_damage | 80-120 | Cracked or broken windows/windshield |
| 9 | structural_damage | 40-80 | Frame or structural component damage |

## Annotation Rules

### 1. Bounding Box Guidelines
- Draw tight bounding boxes around each damage area
- Include minimal background
- For multiple damages of same type: create separate boxes
- Minimum box size: 32x32 pixels

### 2. Quality Standards
- Only annotate clearly visible damage
- Skip blurry or unclear damage areas
- Ensure consistent labeling across similar damages
- Double-check class assignments

### 3. Severity Considerations
- Minor: < 25% of component affected
- Moderate: 25-75% of component affected  
- Severe: > 75% of component affected

### 4. Special Cases
- **Multiple overlapping damages**: Annotate the most severe type
- **Rust/corrosion**: Classify as paint_damage
- **Missing parts**: Classify based on what's missing (e.g., broken_bumper)
- **Reflection/lighting issues**: Skip if damage unclear

## File Naming Convention
```
images/: damage_001.jpg, damage_002.jpg, ...
labels/: damage_001.txt, damage_002.txt, ...
```

## YOLO Label Format
Each line in .txt file:
```
class_id center_x center_y width height
```
All coordinates normalized to 0-1 range.

## Small Dataset Optimization Tips

### 1. Maximize Image Diversity
- Different vehicle makes/models (Peugeot, Renault, Citroën, BMW, Mercedes)
- Various lighting conditions (daylight, shade, artificial light)
- Different angles (front, side, rear, close-up, wide shots)
- Multiple damage severities per type

### 2. Multi-label Strategy
- Annotate ALL visible damages in each image
- One image can contain multiple damage types
- This effectively multiplies your dataset size

### 3. Data Collection Priority
1. **High Priority** (150+ images each):
   - Scratches (surface + deep)
   - Dents (minor + major)
   - Paint damage
2. **Medium Priority** (80-120 images each):
   - Broken bumpers
   - Glass damage
3. **Lower Priority** (40-80 images each):
   - Broken headlights/mirrors
   - Structural damage

## Quality Control for Small Dataset
- Review 20% of annotations (higher than normal due to small size)
- Maintain annotation consistency log
- Daily calibration sessions
- Focus on precision over speed
